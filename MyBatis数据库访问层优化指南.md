# MyBatis 数据库访问层优化指南

## 项目概述

本文档基于NCC Platform数据库架构，提供MyBatis + MySQL的生产级开发规范、SQL优化策略和完整代码示例。

---

## 数据库设计原则

### 1. 命名规范
- 表名：小写字母+下划线，如 `project_2_score`
- 字段名：小写字母+下划线，如 `create_time`
- 索引名：`idx_表名_字段名`，如 `idx_project_score_status`
- 外键名：`fk_表名_字段名`，如 `fk_project_member_project_id`

### 2. 字段设计规范
- 主键：使用 `BIGINT` 类型，自增ID
- 时间字段：使用 `DATETIME` 类型，精确到秒
- 状态字段：使用 `VARCHAR(2)` 或 `TINYINT`
- 金额字段：使用 `DECIMAL(10,2)`
- 布尔字段：使用 `CHAR(1)`，'0'表示false，'1'表示true

### 3. 性能优化原则
- 合理使用索引
- 避免全表扫描
- 控制结果集大小
- 使用批量操作
- 合理使用缓存

---

## 实体类设计

### 1. 基础实体类

```java
package com.ruoyi.common.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Entity基类
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 搜索值 */
    @JsonIgnore
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    /** 开始时间 */
    @JsonIgnore
    private String beginTime;

    /** 结束时间 */
    @JsonIgnore
    private String endTime;

    /** 请求参数 */
    @JsonIgnore
    private Map<String, Object> params;

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
```

### 2. 项目积分实体类

```java
package com.ruoyi.web.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.*;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目积分对象 project_2_score
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel("项目积分对象")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("project_2_score")
public class Project2score extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 项目ID */
    @ApiModelProperty("项目ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    @TableField("project_name")
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")
    private String projectName;

    /** 项目类型 */
    @ApiModelProperty("项目类型")
    @Excel(name = "项目类型", dictType = "project_type")
    @TableField("project_type")
    @NotBlank(message = "项目类型不能为空")
    private String projectType;

    /** 项目描述 */
    @ApiModelProperty("项目描述")
    @Excel(name = "项目描述")
    @TableField("description")
    @Size(max = 500, message = "项目描述不能超过500个字符")
    private String description;

    /** 是否团队项目(0-个人 1-团队) */
    @ApiModelProperty("是否团队项目")
    @Excel(name = "是否团队项目", readConverterExp = "0=个人,1=团队")
    @TableField("is_team")
    @Pattern(regexp = "^[01]$", message = "是否团队项目只能是0或1")
    private String isTeam;

    /** 团队人数 */
    @ApiModelProperty("团队人数")
    @Excel(name = "团队人数")
    @TableField("team_count")
    @Min(value = 1, message = "团队人数不能小于1")
    @Max(value = 10, message = "团队人数不能大于10")
    private Integer teamCount;

    /** 总积分 */
    @ApiModelProperty("总积分")
    @Excel(name = "总积分")
    @TableField("total_score")
    @NotNull(message = "总积分不能为空")
    @DecimalMin(value = "0.01", message = "总积分必须大于0")
    @DecimalMax(value = "100.00", message = "总积分不能超过100")
    private BigDecimal totalScore;

    /** 项目状态(0-草稿 1-待审核 2-已通过 3-已驳回) */
    @ApiModelProperty("项目状态")
    @Excel(name = "项目状态", readConverterExp = "0=草稿,1=待审核,2=已通过,3=已驳回")
    @TableField("status")
    private String status;

    /** 部门ID */
    @ApiModelProperty("部门ID")
    @TableField("dept_id")
    private Long deptId;

    /** 部门名称 */
    @ApiModelProperty("部门名称")
    @TableField(exist = false)
    private String deptName;

    /** 审核人 */
    @ApiModelProperty("审核人")
    @TableField("approval_by")
    private String approvalBy;

    /** 审核时间 */
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("approval_time")
    private Date approvalTime;

    /** 审核备注 */
    @ApiModelProperty("审核备注")
    @TableField("approval_remark")
    private String approvalRemark;

    /** 项目开始时间 */
    @ApiModelProperty("项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("start_date")
    private Date startDate;

    /** 项目结束时间 */
    @ApiModelProperty("项目结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("end_date")
    private Date endDate;

    /** 附件路径 */
    @ApiModelProperty("附件路径")
    @TableField("attachments")
    private String attachments;

    /** 版本号(乐观锁) */
    @ApiModelProperty("版本号")
    @TableField("version")
    @Version
    private Integer version;

    /** 逻辑删除标志(0-正常 1-删除) */
    @ApiModelProperty("删除标志")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    /** 团队成员信息 */
    @ApiModelProperty("团队成员信息")
    @TableField(exist = false)
    private List<Project2scoreMember> members;

    /** 创建者姓名 */
    @ApiModelProperty("创建者姓名")
    @TableField(exist = false)
    private String createByName;

    /** 审核者姓名 */
    @ApiModelProperty("审核者姓名")
    @TableField(exist = false)
    private String approvalByName;
}
```

### 3. 项目成员实体类

```java
package com.ruoyi.web.domain;

import java.math.BigDecimal;

import javax.validation.constraints.*;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目成员对象 project_2_score_member
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel("项目成员对象")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("project_2_score_member")
public class Project2scoreMember extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 成员ID */
    @ApiModelProperty("成员ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 项目ID */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /** 成员姓名 */
    @ApiModelProperty("成员姓名")
    @Excel(name = "成员姓名")
    @TableField("member_name")
    @NotBlank(message = "成员姓名不能为空")
    @Size(min = 2, max = 20, message = "成员姓名长度必须在2-20个字符之间")
    private String memberName;

    /** 成员类型(学生/教师) */
    @ApiModelProperty("成员类型")
    @Excel(name = "成员类型")
    @TableField("member_type")
    @NotBlank(message = "成员类型不能为空")
    @Pattern(regexp = "^(学生|教师)$", message = "成员类型只能是学生或教师")
    private String memberType;

    /** 学号/工号 */
    @ApiModelProperty("学号/工号")
    @Excel(name = "学号/工号")
    @TableField("member_id")
    @NotBlank(message = "学号/工号不能为空")
    @Size(min = 5, max = 20, message = "学号/工号长度必须在5-20个字符之间")
    private String memberId;

    /** 所属部门 */
    @ApiModelProperty("所属部门")
    @Excel(name = "所属部门")
    @TableField("department")
    @NotBlank(message = "所属部门不能为空")
    private String department;

    /** 积分占比 */
    @ApiModelProperty("积分占比")
    @Excel(name = "积分占比")
    @TableField("proportion")
    @NotNull(message = "积分占比不能为空")
    @DecimalMin(value = "0.001", message = "积分占比必须大于0")
    @DecimalMax(value = "1.000", message = "积分占比不能超过1")
    private BigDecimal proportion;

    /** 分配积分 */
    @ApiModelProperty("分配积分")
    @Excel(name = "分配积分")
    @TableField("score")
    private BigDecimal score;

    /** 联系电话 */
    @ApiModelProperty("联系电话")
    @Excel(name = "联系电话")
    @TableField("phone")
    @Size(max = 11, message = "联系电话不能超过11位")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty("邮箱")
    @Excel(name = "邮箱")
    @TableField("email")
    @Size(max = 50, message = "邮箱长度不能超过50个字符")
    @Email(message = "邮箱格式不正确")
    private String email;

    /** 序号 */
    @ApiModelProperty("序号")
    @TableField("sort_order")
    private Integer sortOrder;

    /** 版本号(乐观锁) */
    @ApiModelProperty("版本号")
    @TableField("version")
    @Version
    private Integer version;

    /** 逻辑删除标志(0-正常 1-删除) */
    @ApiModelProperty("删除标志")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;
}
```

---

## Mapper接口设计

### 1. 基础Mapper接口

```java
package com.ruoyi.web.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.domain.dto.Project2scoreQueryDTO;
import com.ruoyi.web.domain.vo.Project2scoreStatisticsVO;
import com.ruoyi.web.domain.vo.DepartmentRankingVO;
import com.ruoyi.web.domain.vo.MonthlyTrendVO;
import com.ruoyi.web.domain.vo.ScoreDistributionVO;

/**
 * 项目积分Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface Project2scoreMapper extends BaseMapper<Project2score> {

    /**
     * 查询项目积分列表
     * 
     * @param queryDTO 查询条件
     * @return 项目积分集合
     */
    List<Project2score> selectProject2scoreList(Project2scoreQueryDTO queryDTO);

    /**
     * 分页查询项目积分列表
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 项目积分分页结果
     */
    IPage<Project2score> selectProject2scorePage(Page<Project2score> page, @Param("query") Project2scoreQueryDTO queryDTO);

    /**
     * 查询项目积分详情
     * 
     * @param id 项目积分主键
     * @return 项目积分
     */
    Project2score selectProject2scoreById(Long id);

    /**
     * 根据项目名称查询项目
     * 
     * @param projectName 项目名称
     * @return 项目积分
     */
    Project2score selectProjectByName(@Param("projectName") String projectName);

    /**
     * 新增项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    int insertProject2score(Project2score project2score);

    /**
     * 修改项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    int updateProject2score(Project2score project2score);

    /**
     * 批量修改项目状态
     * 
     * @param ids 项目ID数组
     * @param status 状态
     * @param updateBy 更新者
     * @return 结果
     */
    int updateProjectStatusBatch(@Param("ids") Long[] ids, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 删除项目积分
     * 
     * @param id 项目积分主键
     * @return 结果
     */
    int deleteProject2scoreById(Long id);

    /**
     * 批量删除项目积分
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProject2scoreByIds(Long[] ids);

    // ==================== 统计查询方法 ====================

    /**
     * 统计项目总数
     * 
     * @param department 部门
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 项目总数
     */
    int countProjects(@Param("department") String department, 
                     @Param("startDate") String startDate, 
                     @Param("endDate") String endDate);

    /**
     * 根据状态统计项目数量
     * 
     * @param status 状态
     * @param department 部门
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 项目数量
     */
    int countProjectsByStatus(@Param("status") String status,
                             @Param("department") String department, 
                             @Param("startDate") String startDate, 
                             @Param("endDate") String endDate);

    /**
     * 统计总积分
     * 
     * @param department 部门
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总积分
     */
    Double sumTotalScore(@Param("department") String department, 
                        @Param("startDate") String startDate, 
                        @Param("endDate") String endDate);

    /**
     * 获取部门排名
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 部门排名列表
     */
    List<DepartmentRankingVO> getDepartmentRanking(@Param("startDate") String startDate, 
                                                   @Param("endDate") String endDate);

    /**
     * 获取月度趋势
     * 
     * @param department 部门
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 月度趋势列表
     */
    List<MonthlyTrendVO> getMonthlyTrend(@Param("department") String department,
                                        @Param("startDate") String startDate, 
                                        @Param("endDate") String endDate);

    /**
     * 获取积分分布
     * 
     * @param department 部门
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 积分分布
     */
    List<ScoreDistributionVO> getScoreDistribution(@Param("department") String department,
                                                  @Param("startDate") String startDate, 
                                                  @Param("endDate") String endDate);

    /**
     * 获取用户项目统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserProjectStatistics(@Param("userId") Long userId);

    /**
     * 获取热门项目类型
     * 
     * @param limit 限制数量
     * @return 项目类型统计
     */
    List<Map<String, Object>> getPopularProjectTypes(@Param("limit") Integer limit);

    // ==================== 优化查询方法 ====================

    /**
     * 批量查询项目基本信息（优化版）
     * 
     * @param ids 项目ID列表
     * @return 项目基本信息列表
     */
    List<Project2score> selectProjectBasicInfoByIds(@Param("ids") List<Long> ids);

    /**
     * 查询待处理项目数量（使用索引优化）
     * 
     * @param createBy 创建者
     * @return 数量
     */
    @Select("SELECT COUNT(1) FROM project_2_score WHERE create_by = #{createBy} AND status = '1' AND del_flag = '0'")
    int countPendingProjectsByUser(@Param("createBy") String createBy);

    /**
     * 更新项目审核信息（乐观锁）
     * 
     * @param id 项目ID
     * @param status 状态
     * @param approvalBy 审核人
     * @param approvalRemark 审核备注
     * @param version 版本号
     * @return 更新结果
     */
    @Update("UPDATE project_2_score SET status = #{status}, approval_by = #{approvalBy}, " +
            "approval_time = NOW(), approval_remark = #{approvalRemark}, version = version + 1, " +
            "update_by = #{approvalBy}, update_time = NOW() " +
            "WHERE id = #{id} AND version = #{version} AND del_flag = '0'")
    int updateProjectApprovalWithVersion(@Param("id") Long id,
                                       @Param("status") String status,
                                       @Param("approvalBy") String approvalBy,
                                       @Param("approvalRemark") String approvalRemark,
                                       @Param("version") Integer version);

    /**
     * 软删除项目（批量）
     * 
     * @param ids 项目ID数组
     * @param updateBy 更新者
     * @return 删除数量
     */
    int softDeleteProjectsByIds(@Param("ids") Long[] ids, @Param("updateBy") String updateBy);
}
```

### 2. 项目成员Mapper接口

```java
package com.ruoyi.web.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.Project2scoreMember;

/**
 * 项目成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface Project2scoreMemberMapper extends BaseMapper<Project2scoreMember> {

    /**
     * 根据项目ID查询成员列表
     * 
     * @param projectId 项目ID
     * @return 成员列表
     */
    List<Project2scoreMember> selectMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID查询成员数量
     * 
     * @param projectId 项目ID
     * @return 成员数量
     */
    @Select("SELECT COUNT(1) FROM project_2_score_member WHERE project_id = #{projectId} AND del_flag = '0'")
    int countMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 新增项目成员
     * 
     * @param member 项目成员
     * @return 结果
     */
    int insertProject2scoreMember(Project2scoreMember member);

    /**
     * 批量新增项目成员
     * 
     * @param members 成员列表
     * @return 结果
     */
    int insertProject2scoreMemberBatch(@Param("members") List<Project2scoreMember> members);

    /**
     * 修改项目成员
     * 
     * @param member 项目成员
     * @return 结果
     */
    int updateProject2scoreMember(Project2scoreMember member);

    /**
     * 删除项目成员
     * 
     * @param id 项目成员主键
     * @return 结果
     */
    int deleteProject2scoreMemberById(Long id);

    /**
     * 根据项目ID删除成员
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    @Delete("DELETE FROM project_2_score_member WHERE project_id = #{projectId}")
    int deleteProject2scoreMemberByProjectId(@Param("projectId") Long projectId);

    /**
     * 批量删除项目成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProject2scoreMemberByIds(Long[] ids);

    /**
     * 根据学号/工号查询成员参与的项目
     * 
     * @param memberId 学号/工号
     * @return 项目成员列表
     */
    List<Project2scoreMember> selectMemberProjectsByMemberId(@Param("memberId") String memberId);

    /**
     * 统计成员参与项目数量
     * 
     * @param memberId 学号/工号
     * @return 项目数量
     */
    int countProjectsByMemberId(@Param("memberId") String memberId);

    /**
     * 获取成员积分汇总
     * 
     * @param memberId 学号/工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 积分汇总
     */
    Double sumScoreByMemberId(@Param("memberId") String memberId,
                             @Param("startDate") String startDate,
                             @Param("endDate") String endDate);
}
```

---

## XML映射文件优化

### 1. 项目积分Mapper XML

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="Project2scoreResult" type="Project2score">
        <result property="id" column="id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="description" column="description"/>
        <result property="isTeam" column="is_team"/>
        <result property="teamCount" column="team_count"/>
        <result property="totalScore" column="total_score"/>
        <result property="status" column="status"/>
        <result property="deptId" column="dept_id"/>
        <result property="approvalBy" column="approval_by"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="attachments" column="attachments"/>
        <result property="version" column="version"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 详细查询映射结果（包含关联数据） -->
    <resultMap id="Project2scoreDetailResult" type="Project2score" extends="Project2scoreResult">
        <result property="deptName" column="dept_name"/>
        <result property="createByName" column="create_by_name"/>
        <result property="approvalByName" column="approval_by_name"/>
        <collection property="members" ofType="Project2scoreMember" column="id" select="com.ruoyi.web.mapper.Project2scoreMemberMapper.selectMembersByProjectId"/>
    </resultMap>

    <!-- 通用查询条件 -->
    <sql id="selectProject2scoreVo">
        SELECT p.id, p.project_name, p.project_type, p.description, p.is_team, p.team_count,
               p.total_score, p.status, p.dept_id, p.approval_by, p.approval_time, p.approval_remark,
               p.start_date, p.end_date, p.attachments, p.version, p.del_flag,
               p.create_by, p.create_time, p.update_by, p.update_time, p.remark
        FROM project_2_score p
    </sql>

    <!-- 详细查询条件（包含关联表） -->
    <sql id="selectProject2scoreDetailVo">
        SELECT p.id, p.project_name, p.project_type, p.description, p.is_team, p.team_count,
               p.total_score, p.status, p.dept_id, p.approval_by, p.approval_time, p.approval_remark,
               p.start_date, p.end_date, p.attachments, p.version, p.del_flag,
               p.create_by, p.create_time, p.update_by, p.update_time, p.remark,
               d.dept_name,
               u1.nick_name as create_by_name,
               u2.nick_name as approval_by_name
        FROM project_2_score p
        LEFT JOIN sys_dept d ON p.dept_id = d.dept_id
        LEFT JOIN sys_user u1 ON p.create_by = u1.user_name
        LEFT JOIN sys_user u2 ON p.approval_by = u2.user_name
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Project2scoreWhere">
        <where>
            p.del_flag = '0'
            <if test="projectName != null and projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{projectName}, '%')
            </if>
            <if test="projectType != null and projectType != ''">
                AND p.project_type = #{projectType}
            </if>
            <if test="status != null and status != ''">
                AND p.status = #{status}
            </if>
            <if test="createBy != null and createBy != ''">
                AND p.create_by = #{createBy}
            </if>
            <if test="isTeam != null and isTeam != ''">
                AND p.is_team = #{isTeam}
            </if>
            <if test="deptId != null and deptId != ''">
                AND p.dept_id = #{deptId}
            </if>
            <if test="minScore != null">
                AND p.total_score >= #{minScore}
            </if>
            <if test="maxScore != null">
                AND p.total_score &lt;= #{maxScore}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND DATE_FORMAT(p.create_time,'%Y-%m-%d') >= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(p.create_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <!-- 数据权限过滤 -->
            <if test="dataScope != null and dataScope != ''">
                AND p.dept_id IN (${dataScope})
            </if>
        </where>
    </sql>

    <!-- 查询项目积分列表 -->
    <select id="selectProject2scoreList" parameterType="Project2scoreQueryDTO" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        <include refid="Project2scoreWhere"/>
        ORDER BY p.create_time DESC
    </select>

    <!-- 分页查询项目积分列表（MyBatis-Plus分页） -->
    <select id="selectProject2scorePage" resultMap="Project2scoreDetailResult">
        <include refid="selectProject2scoreDetailVo"/>
        <include refid="Project2scoreWhere"/>
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询项目积分详情 -->
    <select id="selectProject2scoreById" parameterType="Long" resultMap="Project2scoreDetailResult">
        <include refid="selectProject2scoreDetailVo"/>
        WHERE p.id = #{id} AND p.del_flag = '0'
    </select>

    <!-- 根据项目名称查询 -->
    <select id="selectProjectByName" parameterType="String" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        WHERE p.project_name = #{projectName} AND p.del_flag = '0'
        LIMIT 1
    </select>

    <!-- 新增项目积分 -->
    <insert id="insertProject2score" parameterType="Project2score" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO project_2_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="projectType != null and projectType != ''">project_type,</if>
            <if test="description != null">description,</if>
            <if test="isTeam != null">is_team,</if>
            <if test="teamCount != null">team_count,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="status != null">status,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="attachments != null">attachments,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="projectType != null and projectType != ''">#{projectType},</if>
            <if test="description != null">#{description},</if>
            <if test="isTeam != null">#{isTeam},</if>
            <if test="teamCount != null">#{teamCount},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="status != null">#{status},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            NOW()
        </trim>
    </insert>

    <!-- 修改项目积分 -->
    <update id="updateProject2score" parameterType="Project2score">
        UPDATE project_2_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="projectType != null and projectType != ''">project_type = #{projectType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isTeam != null">is_team = #{isTeam},</if>
            <if test="teamCount != null">team_count = #{teamCount},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="approvalBy != null">approval_by = #{approvalBy},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalRemark != null">approval_remark = #{approvalRemark},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            version = version + 1,
            update_time = NOW()
        </trim>
        WHERE id = #{id} AND version = #{version} AND del_flag = '0'
    </update>

    <!-- 批量修改项目状态 -->
    <update id="updateProjectStatusBatch">
        UPDATE project_2_score 
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 删除项目积分 -->
    <delete id="deleteProject2scoreById" parameterType="Long">
        UPDATE project_2_score SET del_flag = '1' WHERE id = #{id}
    </delete>

    <!-- 批量删除项目积分 -->
    <delete id="deleteProject2scoreByIds" parameterType="String">
        UPDATE project_2_score SET del_flag = '1' WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 软删除项目（批量） -->
    <update id="softDeleteProjectsByIds">
        UPDATE project_2_score 
        SET del_flag = '1', update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- ==================== 统计查询 ==================== -->

    <!-- 统计项目总数 -->
    <select id="countProjects" resultType="int">
        SELECT COUNT(1)
        FROM project_2_score p
        WHERE p.del_flag = '0'
        <if test="department != null and department != ''">
            AND EXISTS (
                SELECT 1 FROM sys_dept d 
                WHERE d.dept_id = p.dept_id AND d.dept_name = #{department}
            )
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 根据状态统计项目数量 -->
    <select id="countProjectsByStatus" resultType="int">
        SELECT COUNT(1)
        FROM project_2_score p
        WHERE p.status = #{status} AND p.del_flag = '0'
        <if test="department != null and department != ''">
            AND EXISTS (
                SELECT 1 FROM sys_dept d 
                WHERE d.dept_id = p.dept_id AND d.dept_name = #{department}
            )
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 统计总积分 -->
    <select id="sumTotalScore" resultType="Double">
        SELECT COALESCE(SUM(p.total_score), 0)
        FROM project_2_score p
        WHERE p.status = '2' AND p.del_flag = '0'
        <if test="department != null and department != ''">
            AND EXISTS (
                SELECT 1 FROM sys_dept d 
                WHERE d.dept_id = p.dept_id AND d.dept_name = #{department}
            )
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 获取部门排名 -->
    <select id="getDepartmentRanking" resultType="com.ruoyi.web.domain.vo.DepartmentRankingVO">
        SELECT 
            d.dept_name as department,
            COUNT(p.id) as projectCount,
            COALESCE(SUM(p.total_score), 0) as totalScore,
            COALESCE(AVG(p.total_score), 0) as averageScore,
            RANK() OVER (ORDER BY COALESCE(SUM(p.total_score), 0) DESC) as ranking
        FROM sys_dept d
        LEFT JOIN project_2_score p ON d.dept_id = p.dept_id 
            AND p.status = '2' AND p.del_flag = '0'
            <if test="startDate != null and startDate != ''">
                AND DATE(p.create_time) >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(p.create_time) &lt;= #{endDate}
            </if>
        WHERE d.status = '0' AND d.del_flag = '0'
        GROUP BY d.dept_id, d.dept_name
        HAVING COUNT(p.id) > 0
        ORDER BY totalScore DESC
        LIMIT 10
    </select>

    <!-- 获取月度趋势 -->
    <select id="getMonthlyTrend" resultType="com.ruoyi.web.domain.vo.MonthlyTrendVO">
        SELECT 
            DATE_FORMAT(p.create_time, '%Y-%m') as month,
            COUNT(p.id) as projectCount,
            COALESCE(SUM(p.total_score), 0) as totalScore
        FROM project_2_score p
        WHERE p.status = '2' AND p.del_flag = '0'
        <if test="department != null and department != ''">
            AND EXISTS (
                SELECT 1 FROM sys_dept d 
                WHERE d.dept_id = p.dept_id AND d.dept_name = #{department}
            )
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
        GROUP BY DATE_FORMAT(p.create_time, '%Y-%m')
        ORDER BY month ASC
    </select>

    <!-- 获取积分分布 -->
    <select id="getScoreDistribution" resultType="com.ruoyi.web.domain.vo.ScoreDistributionVO">
        SELECT 
            CASE 
                WHEN p.total_score >= 0 AND p.total_score &lt; 60 THEN '0-60'
                WHEN p.total_score >= 60 AND p.total_score &lt; 70 THEN '60-70'
                WHEN p.total_score >= 70 AND p.total_score &lt; 80 THEN '70-80'
                WHEN p.total_score >= 80 AND p.total_score &lt; 90 THEN '80-90'
                WHEN p.total_score >= 90 AND p.total_score &lt;= 100 THEN '90-100'
                ELSE 'other'
            END as scoreRange,
            COUNT(p.id) as count
        FROM project_2_score p
        WHERE p.status = '2' AND p.del_flag = '0'
        <if test="department != null and department != ''">
            AND EXISTS (
                SELECT 1 FROM sys_dept d 
                WHERE d.dept_id = p.dept_id AND d.dept_name = #{department}
            )
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
        GROUP BY scoreRange
        ORDER BY scoreRange
    </select>

    <!-- 获取用户项目统计 -->
    <select id="getUserProjectStatistics" resultType="map">
        SELECT 
            COUNT(CASE WHEN status = '0' THEN 1 END) as draftCount,
            COUNT(CASE WHEN status = '1' THEN 1 END) as pendingCount,
            COUNT(CASE WHEN status = '2' THEN 1 END) as approvedCount,
            COUNT(CASE WHEN status = '3' THEN 1 END) as rejectedCount,
            COALESCE(SUM(CASE WHEN status = '2' THEN total_score ELSE 0 END), 0) as totalScore
        FROM project_2_score
        WHERE create_by = (SELECT user_name FROM sys_user WHERE user_id = #{userId})
        AND del_flag = '0'
    </select>

    <!-- 获取热门项目类型 -->
    <select id="getPopularProjectTypes" resultType="map">
        SELECT 
            p.project_type as type,
            d.dict_label as typeName,
            COUNT(p.id) as count,
            COALESCE(AVG(p.total_score), 0) as averageScore
        FROM project_2_score p
        LEFT JOIN sys_dict_data d ON p.project_type = d.dict_value 
            AND d.dict_type = 'project_type' AND d.status = '0'
        WHERE p.status = '2' AND p.del_flag = '0'
        GROUP BY p.project_type, d.dict_label
        ORDER BY count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量查询项目基本信息（优化版） -->
    <select id="selectProjectBasicInfoByIds" resultMap="Project2scoreResult">
        SELECT id, project_name, project_type, total_score, status, create_by, create_time
        FROM project_2_score
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

</mapper>
```

### 2. 项目成员Mapper XML

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreMemberMapper">

    <resultMap type="Project2scoreMember" id="Project2scoreMemberResult">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="memberName" column="member_name"/>
        <result property="memberType" column="member_type"/>
        <result property="memberId" column="member_id"/>
        <result property="department" column="department"/>
        <result property="proportion" column="proportion"/>
        <result property="score" column="score"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="version" column="version"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectProject2scoreMemberVo">
        SELECT id, project_id, member_name, member_type, member_id, department,
               proportion, score, phone, email, sort_order, version, del_flag,
               create_by, create_time, update_by, update_time, remark
        FROM project_2_score_member
    </sql>

    <!-- 根据项目ID查询成员列表 -->
    <select id="selectMembersByProjectId" parameterType="Long" resultMap="Project2scoreMemberResult">
        <include refid="selectProject2scoreMemberVo"/>
        WHERE project_id = #{projectId} AND del_flag = '0'
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <!-- 新增项目成员 -->
    <insert id="insertProject2scoreMember" parameterType="Project2scoreMember" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO project_2_score_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="memberName != null and memberName != ''">member_name,</if>
            <if test="memberType != null and memberType != ''">member_type,</if>
            <if test="memberId != null and memberId != ''">member_id,</if>
            <if test="department != null and department != ''">department,</if>
            <if test="proportion != null">proportion,</if>
            <if test="score != null">score,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="memberName != null and memberName != ''">#{memberName},</if>
            <if test="memberType != null and memberType != ''">#{memberType},</if>
            <if test="memberId != null and memberId != ''">#{memberId},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="proportion != null">#{proportion},</if>
            <if test="score != null">#{score},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            NOW()
        </trim>
    </insert>

    <!-- 批量新增项目成员 -->
    <insert id="insertProject2scoreMemberBatch" parameterType="list">
        INSERT INTO project_2_score_member (
            project_id, member_name, member_type, member_id, department,
            proportion, score, phone, email, sort_order, create_by, create_time
        ) VALUES
        <foreach collection="members" item="member" separator=",">
            (
                #{member.projectId}, #{member.memberName}, #{member.memberType}, 
                #{member.memberId}, #{member.department}, #{member.proportion}, 
                #{member.score}, #{member.phone}, #{member.email}, #{member.sortOrder},
                #{member.createBy}, NOW()
            )
        </foreach>
    </insert>

    <!-- 修改项目成员 -->
    <update id="updateProject2scoreMember" parameterType="Project2scoreMember">
        UPDATE project_2_score_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberName != null and memberName != ''">member_name = #{memberName},</if>
            <if test="memberType != null and memberType != ''">member_type = #{memberType},</if>
            <if test="memberId != null and memberId != ''">member_id = #{memberId},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="proportion != null">proportion = #{proportion},</if>
            <if test="score != null">score = #{score},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            version = version + 1,
            update_time = NOW()
        </trim>
        WHERE id = #{id} AND version = #{version} AND del_flag = '0'
    </update>

    <!-- 删除项目成员 -->
    <delete id="deleteProject2scoreMemberById" parameterType="Long">
        UPDATE project_2_score_member SET del_flag = '1' WHERE id = #{id}
    </delete>

    <!-- 批量删除项目成员 -->
    <delete id="deleteProject2scoreMemberByIds" parameterType="String">
        UPDATE project_2_score_member SET del_flag = '1' WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据学号/工号查询成员参与的项目 -->
    <select id="selectMemberProjectsByMemberId" parameterType="String" resultMap="Project2scoreMemberResult">
        <include refid="selectProject2scoreMemberVo"/>
        WHERE member_id = #{memberId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 统计成员参与项目数量 -->
    <select id="countProjectsByMemberId" parameterType="String" resultType="int">
        SELECT COUNT(DISTINCT project_id)
        FROM project_2_score_member
        WHERE member_id = #{memberId} AND del_flag = '0'
    </select>

    <!-- 获取成员积分汇总 -->
    <select id="sumScoreByMemberId" resultType="Double">
        SELECT COALESCE(SUM(m.score), 0)
        FROM project_2_score_member m
        INNER JOIN project_2_score p ON m.project_id = p.id
        WHERE m.member_id = #{memberId} 
        AND p.status = '2' 
        AND m.del_flag = '0' 
        AND p.del_flag = '0'
        <if test="startDate != null and startDate != ''">
            AND DATE(p.create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(p.create_time) &lt;= #{endDate}
        </if>
    </select>

</mapper>
```

---

## 数据库索引优化

### 1. 索引设计策略

```sql
-- 项目积分表索引
CREATE INDEX idx_project_score_status ON project_2_score(status, del_flag);
CREATE INDEX idx_project_score_create_by ON project_2_score(create_by, del_flag);
CREATE INDEX idx_project_score_dept_id ON project_2_score(dept_id, del_flag);
CREATE INDEX idx_project_score_create_time ON project_2_score(create_time);
CREATE INDEX idx_project_score_project_type ON project_2_score(project_type, del_flag);
CREATE INDEX idx_project_score_name ON project_2_score(project_name);

-- 联合索引（根据查询频率设计）
CREATE INDEX idx_project_score_status_create_time ON project_2_score(status, create_time DESC, del_flag);
CREATE INDEX idx_project_score_dept_status ON project_2_score(dept_id, status, del_flag);

-- 项目成员表索引
CREATE INDEX idx_member_project_id ON project_2_score_member(project_id, del_flag);
CREATE INDEX idx_member_member_id ON project_2_score_member(member_id, del_flag);
CREATE INDEX idx_member_type ON project_2_score_member(member_type, del_flag);

-- 覆盖索引（避免回表查询）
CREATE INDEX idx_project_score_list_cover ON project_2_score(status, del_flag, id, project_name, total_score, create_time);
```

### 2. 查询优化案例

```java
/**
 * SQL优化示例服务类
 */
@Service
public class QueryOptimizationService {

    @Autowired
    private Project2scoreMapper project2scoreMapper;

    /**
     * 优化前：全表扫描
     * SELECT * FROM project_2_score WHERE project_name LIKE '%项目%'
     * 
     * 优化后：使用全文索引或限制查询范围
     */
    public List<Project2score> searchProjectsOptimized(String keyword, String department, String status) {
        Project2scoreQueryDTO queryDTO = new Project2scoreQueryDTO();
        
        // 限制查询范围
        if (StringUtils.isNotEmpty(department)) {
            queryDTO.setDeptId(department);
        }
        if (StringUtils.isNotEmpty(status)) {
            queryDTO.setStatus(status);
        }
        
        // 使用更精确的匹配
        if (StringUtils.isNotEmpty(keyword)) {
            queryDTO.setProjectName(keyword);
        }
        
        return project2scoreMapper.selectProject2scoreList(queryDTO);
    }

    /**
     * 优化前：N+1查询问题
     * 先查询项目列表，再循环查询每个项目的成员
     * 
     * 优化后：使用连接查询一次性获取
     */
    public List<Project2score> getProjectsWithMembersOptimized(List<Long> projectIds) {
        // 批量查询项目基本信息
        List<Project2score> projects = project2scoreMapper.selectProjectBasicInfoByIds(projectIds);
        
        if (CollectionUtils.isEmpty(projects)) {
            return projects;
        }
        
        // 批量查询所有成员信息
        Map<Long, List<Project2scoreMember>> memberMap = projects.stream()
                .map(Project2score::getId)
                .collect(Collectors.toMap(
                    Function.identity(),
                    id -> memberMapper.selectMembersByProjectId(id)
                ));
        
        // 组装数据
        projects.forEach(project -> {
            project.setMembers(memberMap.get(project.getId()));
        });
        
        return projects;
    }

    /**
     * 优化前：大量数据分页查询慢
     * SELECT * FROM project_2_score ORDER BY create_time DESC LIMIT 100000, 10
     * 
     * 优化后：使用游标分页
     */
    public List<Project2score> getProjectsCursorPagination(Long lastId, Integer pageSize) {
        QueryWrapper<Project2score> wrapper = new QueryWrapper<>();
        wrapper.lt(lastId != null, "id", lastId)
               .eq("del_flag", "0")
               .orderByDesc("id")
               .last("LIMIT " + pageSize);
        
        return project2scoreMapper.selectList(wrapper);
    }

    /**
     * 优化前：复杂统计查询慢
     * 多次查询数据库获取不同维度统计
     * 
     * 优化后：使用单次查询获取多维度统计
     */
    public Map<String, Object> getProjectStatisticsOptimized(String department) {
        return project2scoreMapper.getUserProjectStatistics(
            SecurityUtils.getLoginUser().getUser().getUserId()
        );
    }
}
```

---

## MyBatis配置优化

### 1. MyBatis全局配置

```yaml
# application.yml
mybatis:
  # 指定MyBatis配置文件位置
  config-location: classpath:mybatis/mybatis-config.xml
  # 指定Mapper映射文件位置  
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 指定实体类别名包路径
  type-aliases-package: com.ruoyi.**.domain
  # 全局配置
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: true
    # 侵入式延迟加载
    aggressive-lazy-loading: false
    # 允许多种结果集从单独的语句中返回
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许JDBC自动生成主键
    use-generated-keys: true
    # 指定默认执行器类型
    default-executor-type: reuse
    # 指定语句超时时间
    default-statement-timeout: 25000
    # 控制默认获取条数
    default-fetch-size: 100
    # 允许在嵌套语句中使用分页
    safe-result-handler-enabled: false
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 本地缓存机制
    local-cache-scope: session
    # 指定当结果集中值为null时如何处理
    call-setters-on-nulls: false
    # 指定哪个对象的方法触发一次延迟加载
    lazy-load-trigger-methods: equals,clone,hashCode,toString

# MyBatis-Plus配置
mybatis-plus:
  # 启动时检查映射文件是否存在
  check-config-location: true
  # 执行器类型
  executor-type: simple
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      # 主键类型（AUTO为自增）
      id-type: auto
      # 表名前缀
      table-prefix: ""
      # 字段策略
      field-strategy: not_null
      # 逻辑删除字段名
      logic-delete-field: delFlag
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
      # 数据库类型
      db-type: mysql
    # Banner配置
    banner: false
  # 配置
  configuration:
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存
    cache-enabled: true
    # 本地缓存作用域
    local-cache-scope: session
    # 指定当结果集中值为null时如何处理
    call-setters-on-nulls: false
```

### 2. MyBatis核心配置文件

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    
    <!-- 全局参数配置 -->
    <settings>
        <!-- 使用jdbc的getGeneratedKeys获取数据库自增主键值 -->
        <setting name="useGeneratedKeys" value="true" />
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true" />
        <!-- 开启驼峰命名转换 -->
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true" />
        <!-- 延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true" />
        <!-- 侵入式延迟加载 -->
        <setting name="aggressiveLazyLoading" value="false" />
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="25000" />
        <!-- 设置获取数据的条数 -->
        <setting name="defaultFetchSize" value="100" />
        <!-- 日志实现 -->
        <setting name="logImpl" value="SLF4J" />
    </settings>

    <!-- 类型别名 -->
    <typeAliases>
        <typeAlias alias="Long" type="java.lang.Long" />
        <typeAlias alias="String" type="java.lang.String" />
        <typeAlias alias="Integer" type="java.lang.Integer" />
        <typeAlias alias="Double" type="java.lang.Double" />
        <typeAlias alias="HashMap" type="java.util.HashMap" />
        <typeAlias alias="LinkedHashMap" type="java.util.LinkedHashMap" />
        <typeAlias alias="ArrayList" type="java.util.ArrayList" />
        <typeAlias alias="LinkedList" type="java.util.LinkedList" />
    </typeAliases>

    <!-- 类型处理器 -->
    <typeHandlers>
        <!-- 处理JSON字段 -->
        <typeHandler handler="com.ruoyi.common.core.mybatis.JsonTypeHandler" 
                    javaType="java.util.List" jdbcType="VARCHAR"/>
        <!-- 处理枚举类型 -->
        <typeHandler handler="org.apache.ibatis.type.EnumOrdinalTypeHandler" 
                    javaType="com.ruoyi.common.enums.BusinessStatus"/>
    </typeHandlers>

    <!-- 插件配置 -->
    <plugins>
        <!-- 分页插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor">
            <property name="countSqlParser" value="true" />
            <property name="overflow" value="false" />
            <property name="limit" value="500" />
        </plugin>
        <!-- 乐观锁插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor" />
        <!-- SQL性能分析插件（开发环境） -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor">
            <property name="format" value="true" />
            <property name="maxTime" value="3000" />
        </plugin>
    </plugins>

</configuration>
```

---

## 数据库连接池优化

### 1. Druid连接池配置

```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 连接池配置
      initial-size: 10                     # 初始连接数
      min-idle: 10                         # 最小空闲连接数
      max-active: 100                      # 最大活跃连接数
      max-wait: 60000                      # 获取连接等待超时时间
      
      # 检测配置
      time-between-eviction-runs-millis: 60000     # 检测空闲连接间隔时间
      min-evictable-idle-time-millis: 300000       # 空闲连接最小生存时间
      max-evictable-idle-time-millis: 900000       # 空闲连接最大生存时间
      
      # 验证配置
      validation-query: SELECT 1 FROM DUAL          # 验证连接的SQL语句
      validation-query-timeout: 3                   # 验证查询超时时间
      test-while-idle: true                         # 空闲时检测连接是否有效
      test-on-borrow: false                         # 获取连接时检测
      test-on-return: false                         # 归还连接时检测
      
      # 预编译语句缓存
      pool-prepared-statements: true                # 开启PSCache
      max-pool-prepared-statement-per-connection-size: 20  # PSCache大小
      
      # 监控配置
      filters: stat,wall,slf4j                     # 监控过滤器
      use-global-data-source-stat: true            # 合并多个DruidDataSource的监控数据
      
      # 慢SQL记录
      filter:
        stat:
          enabled: true
          slow-sql-millis: 2000                    # 慢SQL阈值
          log-slow-sql: true                       # 记录慢SQL
        wall:
          enabled: true
          config:
            # 允许执行的SQL类型
            select-allow: true
            insert-allow: true
            update-allow: true
            delete-allow: true
            # 禁止高危操作
            drop-table-allow: false
            create-table-allow: false
            alter-table-allow: false
            # 其他安全配置
            multi-statement-allow: false           # 禁止批量执行
            none-base-statement-allow: false       # 禁止非基础语句
        slf4j:
          enabled: true
          statement-log-enabled: true
          statement-executable-sql-log-enable: true
      
      # Web监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        session-stat-enable: true
        session-stat-max-count: 100
      
      # StatViewServlet配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin
        allow: 127.0.0.1,192.168.1.0/24
        deny: 192.168.1.100
```

### 2. HikariCP连接池配置（高性能选择）

```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接池配置
      minimum-idle: 10                             # 最小空闲连接数
      maximum-pool-size: 100                       # 最大连接池大小
      idle-timeout: 600000                         # 空闲连接超时时间(10分钟)
      max-lifetime: 1800000                        # 连接最大生命周期(30分钟)
      connection-timeout: 30000                    # 获取连接超时时间(30秒)
      
      # 连接测试
      connection-test-query: SELECT 1              # 连接测试查询
      validation-timeout: 5000                     # 验证超时时间
      
      # 其他配置
      auto-commit: true                            # 自动提交
      pool-name: HikariCP-Project                  # 连接池名称
      leak-detection-threshold: 60000              # 连接泄漏检测阈值(1分钟)
      
      # 数据库特定配置
      data-source-properties:
        cachePrepStmts: true                       # 缓存预编译语句
        prepStmtCacheSize: 250                     # 预编译语句缓存大小
        prepStmtCacheSqlLimit: 2048                # 预编译语句SQL长度限制
        useServerPrepStmts: true                   # 使用服务端预编译
        useLocalSessionState: true                 # 使用本地会话状态
        rewriteBatchedStatements: true             # 批量语句重写
        cacheResultSetMetadata: true               # 缓存结果集元数据
        cacheServerConfiguration: true             # 缓存服务器配置
        elideSetAutoCommits: true                  # 省略自动提交设置
        maintainTimeStats: false                   # 不维护时间统计
```

---

本文档提供了NCC Platform数据库访问层的完整实现指南，包含了实体类设计、Mapper接口、XML映射文件、索引优化和连接池配置等最佳实践，为数据库操作提供了高性能、高可靠性的技术方案。