# Spring Boot 后端开发最佳实践指南

## 项目概述

本文档基于NCC Platform后端架构，提供Spring Boot + MyBatis + Spring Security的生产级开发规范、设计模式和完整代码示例。

---

## 架构设计原则

### 1. 分层架构设计
- **Controller层**: 接收HTTP请求，参数验证，响应格式化
- **Service层**: 业务逻辑处理，事务管理
- **Mapper层**: 数据访问层，SQL操作
- **Domain层**: 领域模型，实体类定义

### 2. 职责分离原则
- Controller只负责HTTP相关处理
- Service包含核心业务逻辑
- Mapper专注数据库操作
- DTO/VO用于数据传输

### 3. 异常处理策略
- 统一异常处理机制
- 业务异常与系统异常分离
- 友好的错误信息返回

---

## 生产级Controller层实现

### 1. 项目积分管理Controller

```java
package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.domain.dto.Project2scoreDTO;
import com.ruoyi.web.domain.dto.Project2scoreQueryDTO;
import com.ruoyi.web.domain.dto.ApprovalDTO;
import com.ruoyi.web.domain.vo.Project2scoreVO;
import com.ruoyi.web.domain.vo.Project2scoreStatisticsVO;
import com.ruoyi.web.service.IProject2scoreService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目积分管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "项目积分管理")
@RestController
@RequestMapping("/web/project2score")
@Validated
@Slf4j
public class Project2scoreController extends BaseController {
    
    @Autowired
    private IProject2scoreService project2scoreService;

    /**
     * 查询项目积分列表
     */
    @ApiOperation("查询项目积分列表")
    @PreAuthorize("@ss.hasPermi('web:project2score:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Valid Project2scoreQueryDTO queryDTO) {
        log.info("查询项目积分列表，参数: {}", queryDTO);
        
        try {
            startPage();
            List<Project2scoreVO> list = project2scoreService.selectProject2scoreList(queryDTO);
            TableDataInfo dataTable = getDataTable(list);
            
            log.info("查询项目积分列表成功，共{}条记录", dataTable.getTotal());
            return dataTable;
        } catch (Exception e) {
            log.error("查询项目积分列表失败", e);
            throw new ServiceException("查询项目积分列表失败: " + e.getMessage());
        }
    }

    /**
     * 导出项目积分列表
     */
    @ApiOperation("导出项目积分列表")
    @PreAuthorize("@ss.hasPermi('web:project2score:export')")
    @Log(title = "项目积分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody Project2scoreQueryDTO queryDTO) {
        log.info("导出项目积分列表，参数: {}", queryDTO);
        
        try {
            List<Project2scoreVO> list = project2scoreService.selectProject2scoreList(queryDTO);
            ExcelUtil<Project2scoreVO> util = new ExcelUtil<>(Project2scoreVO.class);
            util.exportExcel(response, list, "项目积分数据");
            
            log.info("导出项目积分列表成功，共{}条记录", list.size());
        } catch (Exception e) {
            log.error("导出项目积分列表失败", e);
            throw new ServiceException("导出项目积分列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目积分详细信息
     */
    @ApiOperation("获取项目积分详细信息")
    @PreAuthorize("@ss.hasPermi('web:project2score:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("项目ID") @PathVariable("id") Long id) {
        log.info("获取项目积分详细信息，ID: {}", id);
        
        validateId(id, "项目ID不能为空");
        
        try {
            Project2scoreVO project = project2scoreService.selectProject2scoreById(id);
            if (project == null) {
                return AjaxResult.error("项目不存在或已被删除");
            }
            
            log.info("获取项目积分详细信息成功，项目: {}", project.getProjectName());
            return AjaxResult.success(project);
        } catch (Exception e) {
            log.error("获取项目积分详细信息失败，ID: {}", id, e);
            throw new ServiceException("获取项目详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目积分
     */
    @ApiOperation("新增项目积分")
    @PreAuthorize("@ss.hasPermi('web:project2score:add')")
    @Log(title = "项目积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody Project2scoreDTO project2scoreDTO) {
        log.info("新增项目积分，参数: {}", project2scoreDTO);
        
        try {
            // 业务验证
            validateProjectData(project2scoreDTO);
            
            // 执行新增
            int result = project2scoreService.insertProject2score(project2scoreDTO);
            
            if (result > 0) {
                log.info("新增项目积分成功，项目名称: {}", project2scoreDTO.getProjectName());
                return AjaxResult.success("新增成功");
            } else {
                log.warn("新增项目积分失败，可能是数据重复或其他业务问题");
                return AjaxResult.error("新增失败，请检查数据");
            }
        } catch (ServiceException e) {
            log.error("新增项目积分业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增项目积分系统异常", e);
            throw new ServiceException("新增项目失败: " + e.getMessage());
        }
    }

    /**
     * 修改项目积分
     */
    @ApiOperation("修改项目积分")
    @PreAuthorize("@ss.hasPermi('web:project2score:edit')")
    @Log(title = "项目积分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody Project2scoreDTO project2scoreDTO) {
        log.info("修改项目积分，参数: {}", project2scoreDTO);
        
        validateId(project2scoreDTO.getId(), "项目ID不能为空");
        
        try {
            // 检查项目是否存在及状态
            Project2scoreVO existingProject = project2scoreService.selectProject2scoreById(project2scoreDTO.getId());
            if (existingProject == null) {
                return AjaxResult.error("项目不存在或已被删除");
            }
            
            // 检查是否可以修改
            if (!canEdit(existingProject.getStatus())) {
                return AjaxResult.error("当前状态的项目不允许修改");
            }
            
            // 业务验证
            validateProjectData(project2scoreDTO);
            
            // 执行修改
            int result = project2scoreService.updateProject2score(project2scoreDTO);
            
            if (result > 0) {
                log.info("修改项目积分成功，项目ID: {}", project2scoreDTO.getId());
                return AjaxResult.success("修改成功");
            } else {
                log.warn("修改项目积分失败，项目ID: {}", project2scoreDTO.getId());
                return AjaxResult.error("修改失败");
            }
        } catch (ServiceException e) {
            log.error("修改项目积分业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改项目积分系统异常", e);
            throw new ServiceException("修改项目失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目积分
     */
    @ApiOperation("删除项目积分")
    @PreAuthorize("@ss.hasPermi('web:project2score:remove')")
    @Log(title = "项目积分", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("项目ID数组") @PathVariable Long[] ids) {
        log.info("删除项目积分，IDs: {}", (Object) ids);
        
        if (ids == null || ids.length == 0) {
            return AjaxResult.error("请选择要删除的项目");
        }
        
        try {
            // 批量检查删除权限
            for (Long id : ids) {
                Project2scoreVO project = project2scoreService.selectProject2scoreById(id);
                if (project != null && !canDelete(project.getStatus(), project.getCreateBy())) {
                    return AjaxResult.error("项目【" + project.getProjectName() + "】当前状态不允许删除");
                }
            }
            
            int result = project2scoreService.deleteProject2scoreByIds(ids);
            
            if (result > 0) {
                log.info("删除项目积分成功，删除数量: {}", result);
                return AjaxResult.success("删除成功");
            } else {
                log.warn("删除项目积分失败，IDs: {}", (Object) ids);
                return AjaxResult.error("删除失败");
            }
        } catch (ServiceException e) {
            log.error("删除项目积分业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除项目积分系统异常", e);
            throw new ServiceException("删除项目失败: " + e.getMessage());
        }
    }

    /**
     * 提交审核
     */
    @ApiOperation("提交审核")
    @PreAuthorize("@ss.hasPermi('web:project2score:submit')")
    @Log(title = "项目积分提交审核", businessType = BusinessType.UPDATE)
    @PutMapping("/submit/{id}")
    public AjaxResult submit(@ApiParam("项目ID") @PathVariable("id") Long id) {
        log.info("提交项目审核，ID: {}", id);
        
        validateId(id, "项目ID不能为空");
        
        try {
            boolean result = project2scoreService.submitForApproval(id);
            
            if (result) {
                log.info("提交项目审核成功，ID: {}", id);
                return AjaxResult.success("提交审核成功");
            } else {
                return AjaxResult.error("提交审核失败");
            }
        } catch (ServiceException e) {
            log.error("提交项目审核业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("提交项目审核系统异常", e);
            throw new ServiceException("提交审核失败: " + e.getMessage());
        }
    }

    /**
     * 审核项目
     */
    @ApiOperation("审核项目")
    @PreAuthorize("@ss.hasPermi('web:project2score:approve')")
    @Log(title = "项目积分审核", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@Valid @RequestBody ApprovalDTO approvalDTO) {
        log.info("审核项目，参数: {}", approvalDTO);
        
        try {
            boolean result = project2scoreService.approveProject(approvalDTO);
            
            if (result) {
                String action = "1".equals(approvalDTO.getApprovalResult()) ? "通过" : "驳回";
                log.info("审核项目成功，ID: {}, 结果: {}", approvalDTO.getProjectId(), action);
                return AjaxResult.success("审核" + action + "成功");
            } else {
                return AjaxResult.error("审核失败");
            }
        } catch (ServiceException e) {
            log.error("审核项目业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("审核项目系统异常", e);
            throw new ServiceException("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目统计信息
     */
    @ApiOperation("获取项目统计信息")
    @PreAuthorize("@ss.hasPermi('web:project2score:statistics')")
    @GetMapping("/statistics")
    public AjaxResult statistics(@RequestParam(required = false) String department,
                               @RequestParam(required = false) String startDate,
                               @RequestParam(required = false) String endDate) {
        log.info("获取项目统计信息，部门: {}, 开始时间: {}, 结束时间: {}", department, startDate, endDate);
        
        try {
            Project2scoreStatisticsVO statistics = project2scoreService.getProjectStatistics(department, startDate, endDate);
            
            log.info("获取项目统计信息成功");
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            throw new ServiceException("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入项目积分
     */
    @ApiOperation("批量导入项目积分")
    @PreAuthorize("@ss.hasPermi('web:project2score:import')")
    @Log(title = "项目积分批量导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importData(@RequestParam("file") MultipartFile file) {
        log.info("批量导入项目积分，文件: {}", file.getOriginalFilename());
        
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }
            
            ImportResult result = project2scoreService.importProjectScores(file);
            
            log.info("批量导入项目积分完成，成功: {}, 失败: {}", result.getSuccessCount(), result.getFailureCount());
            return AjaxResult.success("导入完成", result);
        } catch (Exception e) {
            log.error("批量导入项目积分失败", e);
            throw new ServiceException("批量导入失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证ID参数
     */
    private void validateId(Long id, String message) {
        if (id == null || id <= 0) {
            throw new ServiceException(message);
        }
    }

    /**
     * 验证项目数据
     */
    private void validateProjectData(Project2scoreDTO project) {
        // 项目名称重复性检查
        if (project2scoreService.checkProjectNameExists(project.getProjectName(), project.getId())) {
            throw new ServiceException("项目名称已存在");
        }
        
        // 团队项目验证
        if ("1".equals(project.getIsTeam())) {
            if (project.getMembers() == null || project.getMembers().isEmpty()) {
                throw new ServiceException("团队项目必须包含团队成员信息");
            }
            
            if (project.getTeamCount() != project.getMembers().size()) {
                throw new ServiceException("团队人数与成员列表不匹配");
            }
            
            // 验证积分分配比例
            double totalProportion = project.getMembers().stream()
                    .mapToDouble(member -> member.getProportion())
                    .sum();
            
            if (Math.abs(totalProportion - 1.0) > 0.001) {
                throw new ServiceException("团队成员积分占比总和必须等于1");
            }
        }
    }

    /**
     * 检查是否可以编辑
     */
    private boolean canEdit(String status) {
        return "0".equals(status) || "3".equals(status); // 草稿或已驳回状态可编辑
    }

    /**
     * 检查是否可以删除
     */
    private boolean canDelete(String status, String createBy) {
        // 只能删除草稿状态的项目，且只能删除自己创建的项目
        return "0".equals(status) && createBy.equals(SecurityUtils.getUsername());
    }
}
```

### 2. 竞赛管理Controller

```java
package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import com.ruoyi.web.domain.dto.CompetitionDTO;
import com.ruoyi.web.domain.dto.CompetitionQueryDTO;
import com.ruoyi.web.domain.vo.CompetitionVO;
import com.ruoyi.web.service.ICompetitionBaseInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 竞赛基础信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "竞赛管理")
@RestController
@RequestMapping("/web/competitionBaseInfo")
@Slf4j
public class CompetitionBaseInfoController extends BaseController {
    
    @Autowired
    private ICompetitionBaseInfoService competitionBaseInfoService;

    /**
     * 查询竞赛基础信息列表
     */
    @ApiOperation("查询竞赛基础信息列表")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Valid CompetitionQueryDTO queryDTO) {
        log.info("查询竞赛基础信息列表，参数: {}", queryDTO);
        
        startPage();
        List<CompetitionVO> list = competitionBaseInfoService.selectCompetitionList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取竞赛基础信息详细信息
     */
    @ApiOperation("获取竞赛基础信息详细信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        log.info("获取竞赛基础信息详细信息，ID: {}", id);
        
        CompetitionVO competition = competitionBaseInfoService.selectCompetitionById(id);
        return AjaxResult.success(competition);
    }

    /**
     * 新增竞赛基础信息
     */
    @ApiOperation("新增竞赛基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:add')")
    @Log(title = "竞赛基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody CompetitionDTO competitionDTO) {
        log.info("新增竞赛基础信息，参数: {}", competitionDTO);
        
        // 验证竞赛时间的合理性
        validateCompetitionDates(competitionDTO);
        
        int result = competitionBaseInfoService.insertCompetition(competitionDTO);
        return result > 0 ? AjaxResult.success("新增成功") : AjaxResult.error("新增失败");
    }

    /**
     * 修改竞赛基础信息
     */
    @ApiOperation("修改竞赛基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:edit')")
    @Log(title = "竞赛基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody CompetitionDTO competitionDTO) {
        log.info("修改竞赛基础信息，参数: {}", competitionDTO);
        
        validateCompetitionDates(competitionDTO);
        
        int result = competitionBaseInfoService.updateCompetition(competitionDTO);
        return result > 0 ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
    }

    /**
     * 删除竞赛基础信息
     */
    @ApiOperation("删除竞赛基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:remove')")
    @Log(title = "竞赛基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        log.info("删除竞赛基础信息，IDs: {}", (Object) ids);
        
        int result = competitionBaseInfoService.deleteCompetitionByIds(ids);
        return result > 0 ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

    /**
     * 竞赛报名
     */
    @ApiOperation("竞赛报名")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:register')")
    @Log(title = "竞赛报名", businessType = BusinessType.OTHER)
    @PostMapping("/register/{competitionId}")
    public AjaxResult register(@PathVariable Long competitionId, 
                             @RequestBody List<Long> contestantIds) {
        log.info("竞赛报名，竞赛ID: {}, 参赛者IDs: {}", competitionId, contestantIds);
        
        boolean result = competitionBaseInfoService.registerCompetition(competitionId, contestantIds);
        return result ? AjaxResult.success("报名成功") : AjaxResult.error("报名失败");
    }

    private void validateCompetitionDates(CompetitionDTO competition) {
        if (competition.getStartDate() != null && competition.getEndDate() != null) {
            if (competition.getStartDate().after(competition.getEndDate())) {
                throw new ServiceException("开始时间不能晚于结束时间");
            }
        }
        
        if (competition.getRegistrationDeadline() != null && competition.getStartDate() != null) {
            if (competition.getRegistrationDeadline().after(competition.getStartDate())) {
                throw new ServiceException("报名截止时间不能晚于竞赛开始时间");
            }
        }
    }
}
```

---

## 生产级Service层实现

### 1. 项目积分Service实现

```java
package com.ruoyi.web.service.impl;

import java.util.List;
import java.util.Date;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.domain.Project2scoreMember;
import com.ruoyi.web.domain.dto.Project2scoreDTO;
import com.ruoyi.web.domain.dto.Project2scoreQueryDTO;
import com.ruoyi.web.domain.dto.ApprovalDTO;
import com.ruoyi.web.domain.vo.Project2scoreVO;
import com.ruoyi.web.domain.vo.Project2scoreStatisticsVO;
import com.ruoyi.web.mapper.Project2scoreMapper;
import com.ruoyi.web.mapper.Project2scoreMemberMapper;
import com.ruoyi.web.service.IProject2scoreService;
import com.ruoyi.web.service.INotificationService;
import com.ruoyi.web.service.IAuditLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目积分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
@Slf4j
public class Project2scoreServiceImpl implements IProject2scoreService {
    
    @Autowired
    private Project2scoreMapper project2scoreMapper;
    
    @Autowired
    private Project2scoreMemberMapper memberMapper;
    
    @Autowired
    private INotificationService notificationService;
    
    @Autowired
    private IAuditLogService auditLogService;

    /**
     * 查询项目积分列表
     */
    @Override
    public List<Project2scoreVO> selectProject2scoreList(Project2scoreQueryDTO queryDTO) {
        log.debug("查询项目积分列表，参数: {}", queryDTO);
        
        try {
            // 数据权限过滤
            applyDataScope(queryDTO);
            
            List<Project2score> projects = project2scoreMapper.selectProject2scoreList(queryDTO);
            
            // 转换为VO并加载关联数据
            return projects.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询项目积分列表失败，参数: {}", queryDTO, e);
            throw new ServiceException("查询项目列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询项目积分详情
     */
    @Override
    public Project2scoreVO selectProject2scoreById(Long id) {
        log.debug("查询项目积分详情，ID: {}", id);
        
        if (id == null || id <= 0) {
            throw new ServiceException("项目ID不能为空");
        }
        
        try {
            Project2score project = project2scoreMapper.selectProject2scoreById(id);
            if (project == null) {
                return null;
            }
            
            // 检查数据权限
            checkDataPermission(project);
            
            return convertToVO(project);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询项目积分详情失败，ID: {}", id, e);
            throw new ServiceException("查询项目详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertProject2score(Project2scoreDTO projectDTO) {
        log.info("新增项目积分，参数: {}", projectDTO);
        
        try {
            // 业务验证
            validateProjectForInsert(projectDTO);
            
            // 构建实体对象
            Project2score project = buildProjectEntity(projectDTO);
            
            // 插入主表数据
            int result = project2scoreMapper.insertProject2score(project);
            
            if (result > 0) {
                // 处理团队成员
                if ("1".equals(project.getIsTeam()) && !CollectionUtils.isEmpty(projectDTO.getMembers())) {
                    insertProjectMembers(project.getId(), projectDTO.getMembers(), project.getTotalScore());
                }
                
                // 记录操作日志
                auditLogService.recordInsert("项目积分", project.getId(), project.getProjectName());
                
                log.info("新增项目积分成功，ID: {}, 名称: {}", project.getId(), project.getProjectName());
            }
            
            return result;
        } catch (ServiceException e) {
            log.error("新增项目积分业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("新增项目积分系统异常，参数: {}", projectDTO, e);
            throw new ServiceException("新增项目积分失败: " + e.getMessage());
        }
    }

    /**
     * 修改项目积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProject2score(Project2scoreDTO projectDTO) {
        log.info("修改项目积分，参数: {}", projectDTO);
        
        try {
            // 查询原数据
            Project2score existingProject = project2scoreMapper.selectProject2scoreById(projectDTO.getId());
            if (existingProject == null) {
                throw new ServiceException("项目不存在或已被删除");
            }
            
            // 权限检查
            checkUpdatePermission(existingProject);
            
            // 业务验证
            validateProjectForUpdate(projectDTO, existingProject);
            
            // 构建更新实体
            Project2score project = buildProjectEntity(projectDTO);
            project.setUpdateBy(SecurityUtils.getUsername());
            project.setUpdateTime(DateUtils.getNowDate());
            
            // 更新主表数据
            int result = project2scoreMapper.updateProject2score(project);
            
            if (result > 0) {
                // 处理团队成员变更
                updateProjectMembers(project.getId(), projectDTO.getMembers(), project.getTotalScore());
                
                // 记录操作日志
                auditLogService.recordUpdate("项目积分", project.getId(), project.getProjectName(), 
                    buildChangeLog(existingProject, project));
                
                log.info("修改项目积分成功，ID: {}", project.getId());
            }
            
            return result;
        } catch (ServiceException e) {
            log.error("修改项目积分业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("修改项目积分系统异常，参数: {}", projectDTO, e);
            throw new ServiceException("修改项目积分失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除项目积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProject2scoreByIds(Long[] ids) {
        log.info("批量删除项目积分，IDs: {}", (Object) ids);
        
        if (ids == null || ids.length == 0) {
            throw new ServiceException("请选择要删除的项目");
        }
        
        try {
            int deletedCount = 0;
            
            for (Long id : ids) {
                Project2score project = project2scoreMapper.selectProject2scoreById(id);
                if (project != null) {
                    // 检查删除权限
                    checkDeletePermission(project);
                    
                    // 删除团队成员数据
                    memberMapper.deleteProject2scoreMemberByProjectId(id);
                    
                    // 删除主表数据
                    int result = project2scoreMapper.deleteProject2scoreById(id);
                    if (result > 0) {
                        deletedCount++;
                        
                        // 记录操作日志
                        auditLogService.recordDelete("项目积分", id, project.getProjectName());
                    }
                }
            }
            
            log.info("批量删除项目积分完成，删除数量: {}", deletedCount);
            return deletedCount;
        } catch (ServiceException e) {
            log.error("批量删除项目积分业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批量删除项目积分系统异常，IDs: {}", (Object) ids, e);
            throw new ServiceException("批量删除项目积分失败: " + e.getMessage());
        }
    }

    /**
     * 提交审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitForApproval(Long id) {
        log.info("提交项目审核，ID: {}", id);
        
        try {
            Project2score project = project2scoreMapper.selectProject2scoreById(id);
            if (project == null) {
                throw new ServiceException("项目不存在或已被删除");
            }
            
            // 检查状态
            if (!"0".equals(project.getStatus()) && !"3".equals(project.getStatus())) {
                throw new ServiceException("只有草稿或已驳回状态的项目才能提交审核");
            }
            
            // 检查权限
            if (!project.getCreateBy().equals(SecurityUtils.getUsername())) {
                throw new ServiceException("只能提交自己创建的项目");
            }
            
            // 验证项目完整性
            validateProjectForSubmit(project);
            
            // 更新状态为待审核
            project.setStatus("1"); // 待审核
            project.setUpdateBy(SecurityUtils.getUsername());
            project.setUpdateTime(DateUtils.getNowDate());
            
            int result = project2scoreMapper.updateProject2score(project);
            
            if (result > 0) {
                // 发送审核通知
                notificationService.sendApprovalNotification(project);
                
                // 记录操作日志
                auditLogService.recordStatusChange("项目积分", id, project.getProjectName(), "草稿", "待审核");
                
                log.info("提交项目审核成功，ID: {}", id);
                return true;
            }
            
            return false;
        } catch (ServiceException e) {
            log.error("提交项目审核业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("提交项目审核系统异常，ID: {}", id, e);
            throw new ServiceException("提交审核失败: " + e.getMessage());
        }
    }

    /**
     * 审核项目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approveProject(ApprovalDTO approvalDTO) {
        log.info("审核项目，参数: {}", approvalDTO);
        
        try {
            Project2score project = project2scoreMapper.selectProject2scoreById(approvalDTO.getProjectId());
            if (project == null) {
                throw new ServiceException("项目不存在或已被删除");
            }
            
            // 检查状态
            if (!"1".equals(project.getStatus())) {
                throw new ServiceException("只有待审核状态的项目才能进行审核");
            }
            
            // 更新项目状态
            String newStatus = "1".equals(approvalDTO.getApprovalResult()) ? "2" : "3"; // 2-已通过, 3-已驳回
            project.setStatus(newStatus);
            project.setApprovalBy(SecurityUtils.getUsername());
            project.setApprovalTime(DateUtils.getNowDate());
            project.setApprovalRemark(approvalDTO.getApprovalRemark());
            project.setUpdateBy(SecurityUtils.getUsername());
            project.setUpdateTime(DateUtils.getNowDate());
            
            int result = project2scoreMapper.updateProject2score(project);
            
            if (result > 0) {
                // 发送审核结果通知
                notificationService.sendApprovalResultNotification(project, approvalDTO);
                
                // 记录操作日志
                String action = "1".equals(approvalDTO.getApprovalResult()) ? "通过" : "驳回";
                auditLogService.recordApproval("项目积分", project.getId(), project.getProjectName(), 
                    action, approvalDTO.getApprovalRemark());
                
                log.info("审核项目成功，ID: {}, 结果: {}", project.getId(), action);
                return true;
            }
            
            return false;
        } catch (ServiceException e) {
            log.error("审核项目业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("审核项目系统异常，参数: {}", approvalDTO, e);
            throw new ServiceException("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目统计信息
     */
    @Override
    public Project2scoreStatisticsVO getProjectStatistics(String department, String startDate, String endDate) {
        log.debug("获取项目统计信息，部门: {}, 开始时间: {}, 结束时间: {}", department, startDate, endDate);
        
        try {
            Project2scoreStatisticsVO statistics = new Project2scoreStatisticsVO();
            
            // 基础统计
            statistics.setTotalProjects(project2scoreMapper.countProjects(department, startDate, endDate));
            statistics.setCompletedProjects(project2scoreMapper.countProjectsByStatus("2", department, startDate, endDate));
            statistics.setPendingProjects(project2scoreMapper.countProjectsByStatus("1", department, startDate, endDate));
            statistics.setDraftProjects(project2scoreMapper.countProjectsByStatus("0", department, startDate, endDate));
            statistics.setRejectedProjects(project2scoreMapper.countProjectsByStatus("3", department, startDate, endDate));
            
            // 积分统计
            statistics.setTotalScore(project2scoreMapper.sumTotalScore(department, startDate, endDate));
            statistics.setAverageScore(statistics.getTotalProjects() > 0 ? 
                statistics.getTotalScore() / statistics.getTotalProjects() : 0.0);
            
            // 部门排名
            statistics.setDepartmentRanking(project2scoreMapper.getDepartmentRanking(startDate, endDate));
            
            // 月度趋势
            statistics.setMonthlyTrend(project2scoreMapper.getMonthlyTrend(department, startDate, endDate));
            
            // 积分分布
            statistics.setScoreDistribution(project2scoreMapper.getScoreDistribution(department, startDate, endDate));
            
            log.debug("获取项目统计信息成功");
            return statistics;
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            throw new ServiceException("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查项目名称是否存在
     */
    @Override
    public boolean checkProjectNameExists(String projectName, Long excludeId) {
        if (StringUtils.isEmpty(projectName)) {
            return false;
        }
        
        Project2score project = project2scoreMapper.selectProjectByName(projectName);
        return project != null && !project.getId().equals(excludeId);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换为VO对象
     */
    private Project2scoreVO convertToVO(Project2score project) {
        Project2scoreVO vo = new Project2scoreVO();
        BeanUtils.copyBeanProp(vo, project);
        
        // 加载团队成员信息
        if ("1".equals(project.getIsTeam())) {
            List<Project2scoreMember> members = memberMapper.selectMembersByProjectId(project.getId());
            vo.setMembers(members);
        }
        
        return vo;
    }

    /**
     * 构建项目实体对象
     */
    private Project2score buildProjectEntity(Project2scoreDTO dto) {
        Project2score project = new Project2score();
        BeanUtils.copyBeanProp(project, dto);
        
        if (dto.getId() == null) {
            project.setCreateBy(SecurityUtils.getUsername());
            project.setCreateTime(DateUtils.getNowDate());
            project.setStatus("0"); // 草稿状态
        }
        
        return project;
    }

    /**
     * 插入团队成员
     */
    private void insertProjectMembers(Long projectId, List<Project2scoreMember> members, Double totalScore) {
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        
        for (Project2scoreMember member : members) {
            member.setProjectId(projectId);
            member.setScore(member.getProportion() * totalScore);
            member.setCreateBy(SecurityUtils.getUsername());
            member.setCreateTime(DateUtils.getNowDate());
            memberMapper.insertProject2scoreMember(member);
        }
    }

    /**
     * 更新团队成员
     */
    private void updateProjectMembers(Long projectId, List<Project2scoreMember> members, Double totalScore) {
        // 删除原有成员
        memberMapper.deleteProject2scoreMemberByProjectId(projectId);
        
        // 插入新成员
        if (!CollectionUtils.isEmpty(members)) {
            insertProjectMembers(projectId, members, totalScore);
        }
    }

    /**
     * 应用数据权限
     */
    private void applyDataScope(Project2scoreQueryDTO queryDTO) {
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        
        // 根据用户角色和部门设置数据权限
        if (!SecurityUtils.isAdmin(currentUser.getUserId())) {
            queryDTO.setDataScope(currentUser.getDept().getDeptId().toString());
        }
    }

    /**
     * 检查数据权限
     */
    private void checkDataPermission(Project2score project) {
        if (!SecurityUtils.isAdmin()) {
            SysUser currentUser = SecurityUtils.getLoginUser().getUser();
            
            // 只能查看自己部门的数据或自己创建的数据
            if (!project.getCreateBy().equals(currentUser.getUserName()) && 
                !currentUser.getDept().getDeptId().toString().equals(project.getDeptId())) {
                throw new ServiceException("没有权限访问该项目");
            }
        }
    }

    /**
     * 检查更新权限
     */
    private void checkUpdatePermission(Project2score project) {
        String currentUser = SecurityUtils.getUsername();
        
        // 只能修改自己创建的项目
        if (!project.getCreateBy().equals(currentUser)) {
            throw new ServiceException("只能修改自己创建的项目");
        }
        
        // 检查状态
        if (!"0".equals(project.getStatus()) && !"3".equals(project.getStatus())) {
            throw new ServiceException("当前状态的项目不允许修改");
        }
    }

    /**
     * 检查删除权限
     */
    private void checkDeletePermission(Project2score project) {
        String currentUser = SecurityUtils.getUsername();
        
        // 只能删除自己创建的草稿状态项目
        if (!project.getCreateBy().equals(currentUser)) {
            throw new ServiceException("只能删除自己创建的项目");
        }
        
        if (!"0".equals(project.getStatus())) {
            throw new ServiceException("只能删除草稿状态的项目");
        }
    }

    /**
     * 验证新增项目
     */
    private void validateProjectForInsert(Project2scoreDTO project) {
        // 基础验证
        validateBasicProjectInfo(project);
        
        // 检查项目名称唯一性
        if (checkProjectNameExists(project.getProjectName(), null)) {
            throw new ServiceException("项目名称已存在");
        }
    }

    /**
     * 验证更新项目
     */
    private void validateProjectForUpdate(Project2scoreDTO project, Project2score existing) {
        // 基础验证
        validateBasicProjectInfo(project);
        
        // 检查项目名称唯一性
        if (checkProjectNameExists(project.getProjectName(), project.getId())) {
            throw new ServiceException("项目名称已存在");
        }
    }

    /**
     * 基础项目信息验证
     */
    private void validateBasicProjectInfo(Project2scoreDTO project) {
        if (StringUtils.isEmpty(project.getProjectName())) {
            throw new ServiceException("项目名称不能为空");
        }
        
        if (project.getTotalScore() == null || project.getTotalScore() <= 0) {
            throw new ServiceException("总积分必须大于0");
        }
        
        // 团队项目验证
        if ("1".equals(project.getIsTeam())) {
            validateTeamProject(project);
        }
    }

    /**
     * 验证团队项目
     */
    private void validateTeamProject(Project2scoreDTO project) {
        if (CollectionUtils.isEmpty(project.getMembers())) {
            throw new ServiceException("团队项目必须包含团队成员信息");
        }
        
        if (project.getTeamCount() != project.getMembers().size()) {
            throw new ServiceException("团队人数与成员列表不匹配");
        }
        
        // 验证积分分配比例
        double totalProportion = project.getMembers().stream()
                .mapToDouble(Project2scoreMember::getProportion)
                .sum();
        
        if (Math.abs(totalProportion - 1.0) > 0.001) {
            throw new ServiceException("团队成员积分占比总和必须等于1");
        }
        
        // 验证成员信息完整性
        for (Project2scoreMember member : project.getMembers()) {
            if (StringUtils.isEmpty(member.getMemberName())) {
                throw new ServiceException("成员姓名不能为空");
            }
            if (StringUtils.isEmpty(member.getMemberType())) {
                throw new ServiceException("成员类型不能为空");
            }
            if (StringUtils.isEmpty(member.getMemberId())) {
                throw new ServiceException("学号/工号不能为空");
            }
        }
    }

    /**
     * 验证项目提交
     */
    private void validateProjectForSubmit(Project2score project) {
        if (StringUtils.isEmpty(project.getDescription())) {
            throw new ServiceException("项目描述不能为空，请完善项目信息后再提交");
        }
        
        // 团队项目需要验证成员信息
        if ("1".equals(project.getIsTeam())) {
            List<Project2scoreMember> members = memberMapper.selectMembersByProjectId(project.getId());
            if (CollectionUtils.isEmpty(members)) {
                throw new ServiceException("团队项目必须包含成员信息，请完善后再提交");
            }
        }
    }

    /**
     * 构建变更日志
     */
    private String buildChangeLog(Project2score oldProject, Project2score newProject) {
        StringBuilder log = new StringBuilder();
        
        if (!oldProject.getProjectName().equals(newProject.getProjectName())) {
            log.append("项目名称: ").append(oldProject.getProjectName())
               .append(" → ").append(newProject.getProjectName()).append("; ");
        }
        
        if (!oldProject.getTotalScore().equals(newProject.getTotalScore())) {
            log.append("总积分: ").append(oldProject.getTotalScore())
               .append(" → ").append(newProject.getTotalScore()).append("; ");
        }
        
        return log.toString();
    }
}
```

---

## 数据传输对象(DTO/VO)设计

### 1. 请求DTO

```java
package com.ruoyi.web.domain.dto;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.web.domain.Project2scoreMember;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目积分请求DTO
 */
@ApiModel("项目积分请求对象")
@Data
public class Project2scoreDTO {
    
    @ApiModelProperty("项目ID")
    private Long id;
    
    @ApiModelProperty(value = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")
    private String projectName;
    
    @ApiModelProperty(value = "项目类型", required = true)
    @NotBlank(message = "项目类型不能为空")
    private String projectType;
    
    @ApiModelProperty("项目描述")
    @Size(max = 500, message = "项目描述不能超过500个字符")
    private String description;
    
    @ApiModelProperty(value = "是否团队项目", required = true)
    @NotBlank(message = "是否团队项目不能为空")
    @Pattern(regexp = "^[01]$", message = "是否团队项目只能是0或1")
    private String isTeam;
    
    @ApiModelProperty("团队人数")
    @Min(value = 1, message = "团队人数不能小于1")
    @Max(value = 10, message = "团队人数不能大于10")
    private Integer teamCount;
    
    @ApiModelProperty(value = "总积分", required = true)
    @NotNull(message = "总积分不能为空")
    @DecimalMin(value = "0.01", message = "总积分必须大于0")
    @DecimalMax(value = "100.00", message = "总积分不能超过100")
    private Double totalScore;
    
    @ApiModelProperty("团队成员列表")
    @Valid
    private List<Project2scoreMember> members;
    
    @ApiModelProperty("附件列表")
    private String attachments;
    
    @ApiModelProperty("项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @ApiModelProperty("项目结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
```

### 2. 查询DTO

```java
package com.ruoyi.web.domain.dto;

import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目积分查询DTO
 */
@ApiModel("项目积分查询对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class Project2scoreQueryDTO extends BaseEntity {
    
    @ApiModelProperty("项目名称")
    private String projectName;
    
    @ApiModelProperty("项目类型")
    private String projectType;
    
    @ApiModelProperty("项目状态")
    private String status;
    
    @ApiModelProperty("创建者")
    private String createBy;
    
    @ApiModelProperty("是否团队项目")
    private String isTeam;
    
    @ApiModelProperty("部门ID")
    private String deptId;
    
    @ApiModelProperty("数据权限范围")
    private String dataScope;
    
    @ApiModelProperty("最小积分")
    private Double minScore;
    
    @ApiModelProperty("最大积分")
    private Double maxScore;
}
```

### 3. 响应VO

```java
package com.ruoyi.web.domain.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.web.domain.Project2scoreMember;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目积分响应VO
 */
@ApiModel("项目积分响应对象")
@Data
public class Project2scoreVO {
    
    @ApiModelProperty("项目ID")
    private Long id;
    
    @ApiModelProperty("项目名称")
    private String projectName;
    
    @ApiModelProperty("项目类型")
    private String projectType;
    
    @ApiModelProperty("项目类型名称")
    private String projectTypeName;
    
    @ApiModelProperty("项目描述")
    private String description;
    
    @ApiModelProperty("是否团队项目")
    private String isTeam;
    
    @ApiModelProperty("团队人数")
    private Integer teamCount;
    
    @ApiModelProperty("总积分")
    private Double totalScore;
    
    @ApiModelProperty("项目状态")
    private String status;
    
    @ApiModelProperty("项目状态名称")
    private String statusName;
    
    @ApiModelProperty("创建者")
    private String createBy;
    
    @ApiModelProperty("创建者姓名")
    private String createByName;
    
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    @ApiModelProperty("审核人")
    private String approvalBy;
    
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;
    
    @ApiModelProperty("审核备注")
    private String approvalRemark;
    
    @ApiModelProperty("团队成员列表")
    private List<Project2scoreMember> members;
    
    @ApiModelProperty("附件列表")
    private List<AttachmentVO> attachmentList;
    
    @ApiModelProperty("是否可编辑")
    private Boolean canEdit;
    
    @ApiModelProperty("是否可删除")
    private Boolean canDelete;
    
    @ApiModelProperty("是否可提交")
    private Boolean canSubmit;
}
```

---

## 异常处理与事务管理

### 1. 全局异常处理器

```java
package com.ruoyi.framework.web.exception;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.DemoModeException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request) {
        log.error("业务异常，请求地址'{}', 异常信息'{}'", request.getRequestURI(), e.getMessage());
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, e.getMessage()) : AjaxResult.error(e.getMessage());
    }

    /**
     * 请求参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public AjaxResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.error("参数校验异常，请求地址'{}'", request.getRequestURI(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return AjaxResult.error(message);
    }

    /**
     * 请求参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e, HttpServletRequest request) {
        log.error("参数绑定异常，请求地址'{}'", request.getRequestURI(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return AjaxResult.error(message);
    }

    /**
     * 参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public AjaxResult handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.error("参数校验异常，请求地址'{}'", request.getRequestURI(), e);
        String message = e.getConstraintViolations().stream()
                .findFirst()
                .map(ConstraintViolation::getMessage)
                .orElse("参数校验失败");
        return AjaxResult.error(message);
    }

    /**
     * 请求路径中缺少必需的路径变量
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public AjaxResult handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request) {
        log.error("请求路径中缺少必需的路径变量'{}', 请求地址'{}'", e.getVariableName(), request.getRequestURI());
        return AjaxResult.error(String.format("请求路径中缺少必需的路径变量[%s]", e.getVariableName()));
    }

    /**
     * 请求参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public AjaxResult handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.error("请求参数类型不匹配'{}', 请求地址'{}'", e.getName(), request.getRequestURI());
        return AjaxResult.error(String.format("请求参数类型不匹配，参数[%s]要求类型为：%s", e.getName(), e.getRequiredType().getName()));
    }

    /**
     * 不支持的请求方法
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.error("不支持'{}'请求方法, 请求地址'{}'", e.getMethod(), request.getRequestURI());
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public AjaxResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        log.error("请求地址'{}', 权限校验失败'{}'", request.getRequestURI(), e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public AjaxResult handleDemoModeException(DemoModeException e) {
        return AjaxResult.error("演示模式，不允许操作");
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        log.error("请求地址'{}', 发生未知异常.", request.getRequestURI(), e);
        return AjaxResult.error("系统异常，请联系管理员");
    }
}
```

### 2. 事务管理配置

```java
package com.ruoyi.framework.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.interceptor.TransactionInterceptor;
import org.springframework.transaction.interceptor.RollbackRuleAttribute;
import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;
import org.springframework.transaction.interceptor.TransactionAttribute;

import java.util.HashMap;
import java.util.Map;

/**
 * 事务管理配置
 */
@Configuration
@EnableTransactionManagement
public class TransactionConfig {

    /**
     * 声明式事务配置
     */
    @Bean
    public TransactionInterceptor transactionInterceptor() {
        RuleBasedTransactionAttribute readOnlyRule = new RuleBasedTransactionAttribute();
        readOnlyRule.setReadOnly(true);
        readOnlyRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_SUPPORTS);

        RuleBasedTransactionAttribute requiredRule = new RuleBasedTransactionAttribute();
        requiredRule.setRollbackRules(RollbackRuleAttribute.EXCEPTION);
        requiredRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        requiredRule.setTimeout(30); // 30秒超时

        Map<String, TransactionAttribute> txMap = new HashMap<>();
        
        // 只读事务
        txMap.put("get*", readOnlyRule);
        txMap.put("select*", readOnlyRule);
        txMap.put("query*", readOnlyRule);
        txMap.put("find*", readOnlyRule);
        txMap.put("list*", readOnlyRule);
        txMap.put("count*", readOnlyRule);
        txMap.put("check*", readOnlyRule);
        
        // 读写事务
        txMap.put("add*", requiredRule);
        txMap.put("insert*", requiredRule);
        txMap.put("save*", requiredRule);
        txMap.put("update*", requiredRule);
        txMap.put("modify*", requiredRule);
        txMap.put("edit*", requiredRule);
        txMap.put("delete*", requiredRule);
        txMap.put("remove*", requiredRule);
        txMap.put("approve*", requiredRule);
        txMap.put("submit*", requiredRule);
        txMap.put("import*", requiredRule);
        txMap.put("export*", requiredRule);

        return new TransactionInterceptor(null, txMap);
    }
}
```

---

## 缓存策略与性能优化

### 1. Redis缓存配置

```java
package com.ruoyi.framework.config;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis缓存配置
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory factory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        // 不同业务不同缓存配置
        Map<String, RedisCacheConfiguration> configMap = new HashMap<>();
        
        // 项目积分缓存30分钟
        configMap.put("project2score", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 用户信息缓存1小时
        configMap.put("userInfo", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 字典数据缓存2小时
        configMap.put("dictData", defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // 统计数据缓存10分钟
        configMap.put("statistics", defaultConfig.entryTtl(Duration.ofMinutes(10)));

        return RedisCacheManager.builder(factory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(configMap)
                .build();
    }
}
```

### 2. 缓存使用示例

```java
@Service
public class Project2scoreServiceImpl implements IProject2scoreService {

    @Cacheable(value = "project2score", key = "#id", unless = "#result == null")
    @Override
    public Project2scoreVO selectProject2scoreById(Long id) {
        // 实现逻辑
    }

    @CacheEvict(value = "project2score", key = "#projectDTO.id")
    @Override
    public int updateProject2score(Project2scoreDTO projectDTO) {
        // 实现逻辑
    }

    @CacheEvict(value = "project2score", allEntries = true)
    @Override
    public int deleteProject2scoreByIds(Long[] ids) {
        // 实现逻辑
    }

    @Cacheable(value = "statistics", key = "'dept_' + #department + '_' + #startDate + '_' + #endDate")
    @Override
    public Project2scoreStatisticsVO getProjectStatistics(String department, String startDate, String endDate) {
        // 实现逻辑
    }
}
```

---

本文档提供了NCC Platform后端开发的完整指南，包含了Controller层、Service层的生产级实现，以及DTO/VO设计、异常处理、事务管理和缓存策略等最佳实践，为团队开发提供了标准化的技术规范。