# NCC Platform 生产级API文档与示例

## 项目概述

NCC Platform 是基于 RuoYi-Vue 框架构建的高校竞赛与科研积分管理平台，采用前后端分离架构，提供完整的竞赛管理、项目积分评定、用户权限管理等功能。

---

## API 架构设计

### 统一响应格式

所有API接口遵循统一的响应格式：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 状态码规范

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 401 | 未授权 | 用户未登录或token过期 |
| 403 | 禁止访问 | 用户权限不足 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 核心业务模块API

### 1. 用户认证模块

#### 1.1 用户登录

**接口地址：** `POST /login`

**请求参数：**
```json
{
  "username": "admin",
  "password": "admin123",
  "code": "1234",
  "uuid": "captcha-uuid"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "Bearer eyJhbGciOiJIUzUxMiJ9...",
    "user": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "15888888888",
      "sex": "1",
      "avatar": "",
      "status": "0",
      "deptId": 103,
      "roles": [
        {
          "roleId": 1,
          "roleName": "超级管理员",
          "roleKey": "admin",
          "roleSort": "1",
          "dataScope": "1",
          "status": "0"
        }
      ],
      "permissions": [
        "*:*:*"
      ]
    }
  }
}
```

**实现示例：**
```java
@RestController
public class SysLoginController {
    
    @Autowired
    private SysLoginService loginService;
    
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), 
                                         loginBody.getPassword(), 
                                         loginBody.getCode(),
                                         loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
}
```

### 2. 项目积分管理模块

#### 2.1 项目积分查询

**接口地址：** `GET /web/project2score/list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "projectName": "项目名称",
  "status": "2",
  "createBy": "user123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "id": 1,
        "projectName": "机器学习算法优化研究",
        "projectType": "1",
        "isTeam": "1",
        "teamCount": 3,
        "totalScore": 85.5,
        "status": "2",
        "createBy": "teacher001",
        "createTime": "2024-01-15 10:30:00",
        "updateTime": "2024-01-20 14:22:00",
        "members": [
          {
            "memberId": 1,
            "memberName": "张三",
            "memberType": "学生",
            "score": 28.5,
            "proportion": 0.33
          },
          {
            "memberId": 2,
            "memberName": "李四",
            "memberType": "学生", 
            "score": 28.5,
            "proportion": 0.33
          },
          {
            "memberId": 3,
            "memberName": "王老师",
            "memberType": "教师",
            "score": 28.5,
            "proportion": 0.34
          }
        ]
      }
    ]
  }
}
```

**Controller实现：**
```java
@RestController
@RequestMapping("/web/project2score")
public class Project2scoreController extends BaseController {
    
    @Autowired
    private IProject2scoreService project2scoreService;
    
    @PreAuthorize("@ss.hasPermi('web:project2score:list')")
    @GetMapping("/list")
    public TableDataInfo list(Project2score project2score) {
        startPage();
        List<Project2score> list = project2scoreService.selectProject2scoreList(project2score);
        return getDataTable(list);
    }
}
```

#### 2.2 新增项目积分

**接口地址：** `POST /web/project2score`

**请求参数：**
```json
{
  "projectName": "深度学习模型优化",
  "projectType": "1",
  "description": "基于神经网络的图像识别优化研究",
  "isTeam": "1",
  "teamCount": 4,
  "totalScore": 90.0,
  "members": [
    {
      "memberName": "学生A",
      "memberType": "学生",
      "studentId": "20210001",
      "department": "计算机学院",
      "proportion": 0.25
    },
    {
      "memberName": "学生B", 
      "memberType": "学生",
      "studentId": "20210002",
      "department": "计算机学院",
      "proportion": 0.25
    },
    {
      "memberName": "学生C",
      "memberType": "学生", 
      "studentId": "20210003",
      "department": "计算机学院",
      "proportion": 0.25
    },
    {
      "memberName": "指导老师",
      "memberType": "教师",
      "teacherId": "T001",
      "department": "计算机学院",
      "proportion": 0.25
    }
  ]
}
```

**Service层实现：**
```java
@Service
public class Project2scoreServiceImpl implements IProject2scoreService {
    
    @Autowired
    private Project2scoreMapper project2scoreMapper;
    
    @Autowired
    private Project2scoreMemberMapper memberMapper;
    
    @Override
    @Transactional
    public int insertProject2score(Project2score project2score) {
        // 设置创建信息
        project2score.setCreateBy(SecurityUtils.getUsername());
        project2score.setCreateTime(DateUtils.getNowDate());
        project2score.setStatus("0"); // 草稿状态
        
        // 插入主记录
        int result = project2scoreMapper.insertProject2score(project2score);
        
        // 插入团队成员记录
        if ("1".equals(project2score.getIsTeam()) && 
            CollectionUtils.isNotEmpty(project2score.getMembers())) {
            
            insertProjectMembers(project2score);
        }
        
        return result;
    }
    
    private void insertProjectMembers(Project2score project2score) {
        List<Project2scoreMember> members = project2score.getMembers();
        for (Project2scoreMember member : members) {
            member.setProjectId(project2score.getId());
            member.setScore(project2score.getTotalScore() * member.getProportion());
            memberMapper.insertProject2scoreMember(member);
        }
    }
}
```

### 3. 竞赛管理模块

#### 3.1 竞赛信息查询

**接口地址：** `GET /web/competitionBaseInfo/list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "competitionName": "数学建模竞赛",
  "status": "2",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功", 
  "data": {
    "total": 50,
    "rows": [
      {
        "id": 1,
        "competitionName": "全国大学生数学建模竞赛",
        "competitionLevel": "国家级",
        "organizer": "教育部高等教育司",
        "startDate": "2024-09-15",
        "endDate": "2024-09-18",
        "registrationDeadline": "2024-08-31",
        "maxParticipants": 3,
        "status": "2",
        "description": "面向全国大学生的数学建模竞赛活动",
        "attachments": [
          {
            "fileName": "竞赛通知.pdf",
            "filePath": "/upload/2024/01/15/notice.pdf",
            "fileSize": "1.2MB"
          }
        ],
        "instructors": [
          {
            "instructorId": 1,
            "instructorName": "张教授",
            "department": "数学学院",
            "phone": "13800138000",
            "email": "<EMAIL>"
          }
        ],
        "contestants": [
          {
            "contestantId": 1,
            "studentName": "李明",
            "studentId": "20210001",
            "department": "计算机学院",
            "grade": "2021级",
            "phone": "13900139000"
          }
        ]
      }
    ]
  }
}
```

### 4. 数据统计分析模块

#### 4.1 积分统计分析

**接口地址：** `GET /web/scoreAnalysis/statistics`

**请求参数：**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "department": "计算机学院",
  "analysisType": "department"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "统计成功",
  "data": {
    "summary": {
      "totalProjects": 156,
      "totalScore": 12580.5,
      "averageScore": 80.6,
      "completedProjects": 145,
      "pendingProjects": 11
    },
    "departmentRanking": [
      {
        "department": "计算机学院",
        "projectCount": 45,
        "totalScore": 3850.5,
        "averageScore": 85.6,
        "ranking": 1
      },
      {
        "department": "信息学院", 
        "projectCount": 38,
        "totalScore": 3120.0,
        "averageScore": 82.1,
        "ranking": 2
      }
    ],
    "monthlyTrend": [
      {
        "month": "2024-01",
        "projectCount": 12,
        "totalScore": 980.5
      },
      {
        "month": "2024-02",
        "projectCount": 15,
        "totalScore": 1205.0
      }
    ],
    "scoreDistribution": {
      "0-60": 8,
      "60-70": 25,
      "70-80": 45,
      "80-90": 52,
      "90-100": 26
    }
  }
}
```

---

## 权限控制与安全

### JWT Token 认证

```java
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    
    @Autowired
    private TokenService tokenService;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain chain) throws ServletException, IOException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser) && 
            StringUtils.isNull(SecurityUtils.getAuthentication())) {
            tokenService.verifyToken(loginUser);
            UsernamePasswordAuthenticationToken authenticationToken = 
                new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }
        chain.doFilter(request, response);
    }
}
```

### 权限注解使用

```java
// 检查用户权限
@PreAuthorize("@ss.hasPermi('web:project2score:add')")
public AjaxResult add(@RequestBody Project2score project2score) {
    // 业务逻辑
}

// 检查角色权限
@PreAuthorize("@ss.hasRole('admin')")
public AjaxResult adminOperation() {
    // 管理员专用功能
}

// 数据权限控制
@DataScope(deptAlias = "d", userAlias = "u")
public List<Project2score> selectProjectList(Project2score project2score) {
    // 根据用户所属部门过滤数据
}
```

---

## 错误处理与异常规范

### 全局异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public AjaxResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限校验失败'{}'", requestURI, e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }
    
    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, e.getMessage()) : AjaxResult.error(e.getMessage());
    }
    
    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);
        return AjaxResult.error(e.getMessage());
    }
}
```

### 业务异常定义

```java
public class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    
    private Integer code;
    
    private String message;
    
    public ServiceException() {
    }
    
    public ServiceException(String message) {
        this.message = message;
    }
    
    public ServiceException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
    
    // 常用业务异常
    public static final ServiceException PARAM_ERROR = new ServiceException("参数错误", 400);
    public static final ServiceException DATA_NOT_FOUND = new ServiceException("数据不存在", 404);
    public static final ServiceException PERMISSION_DENIED = new ServiceException("权限不足", 403);
}
```

---

## API接口测试示例

### 使用 RestTemplate 测试

```java
@SpringBootTest
public class ApiIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    private String token;
    
    @BeforeEach
    void login() {
        LoginBody loginBody = new LoginBody();
        loginBody.setUsername("admin");
        loginBody.setPassword("admin123");
        
        ResponseEntity<AjaxResult> response = restTemplate.postForEntity("/login", loginBody, AjaxResult.class);
        token = (String) response.getBody().get("token");
    }
    
    @Test
    void testProjectScoreList() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
            "/web/project2score/list?pageNum=1&pageSize=10",
            HttpMethod.GET,
            entity,
            String.class
        );
        
        assertEquals(200, response.getStatusCodeValue());
    }
}
```

---

## 性能优化建议

### 1. 数据库查询优化

```java
// 使用 MyBatis-Plus 的分页查询
@Override
public IPage<Project2score> selectProjectPage(Page<Project2score> page, Project2score project2score) {
    QueryWrapper<Project2score> queryWrapper = new QueryWrapper<>();
    queryWrapper.like(StringUtils.isNotEmpty(project2score.getProjectName()), 
                     "project_name", project2score.getProjectName())
                .eq(StringUtils.isNotEmpty(project2score.getStatus()), 
                   "status", project2score.getStatus())
                .orderByDesc("create_time");
    
    return project2scoreMapper.selectPage(page, queryWrapper);
}
```

### 2. Redis缓存使用

```java
@Service
public class Project2scoreServiceImpl implements IProject2scoreService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Cacheable(value = "project2score", key = "#id")
    public Project2score selectProject2scoreById(Long id) {
        return project2scoreMapper.selectProject2scoreById(id);
    }
    
    @CacheEvict(value = "project2score", key = "#project2score.id")
    public int updateProject2score(Project2score project2score) {
        return project2scoreMapper.updateProject2score(project2score);
    }
}
```

### 3. 异步处理

```java
@Service
public class AsyncScoreService {
    
    @Async("taskExecutor")
    public CompletableFuture<Void> calculateBatchScores(List<Project2score> projects) {
        for (Project2score project : projects) {
            // 复杂的积分计算逻辑
            calculateProjectScore(project);
        }
        return CompletableFuture.completedFuture(null);
    }
}
```

---

## 部署配置

### application.yml 生产环境配置

```yaml
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

spring:
  profiles:
    active: prod
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      url: *****************************************************************************************************************************************************
      username: ${DB_USERNAME:ncc_user}
      password: ${DB_PASSWORD:ncc_password}
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
  config: classpath:logback-spring.xml

# 用户配置
user:
  password:
    maxRetryCount: 5
    lockTime: 10

# token配置
token:
  header: Authorization
  secret: abcdefghijklmnopqrstuvwxyz
  expireTime: 30

# MyBatis配置
mybatis:
  typeAliasesPackage: com.ruoyi.**.domain
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml
```

---

本文档提供了NCC Platform的核心API接口设计、实现示例和最佳实践，为生产环境部署和维护提供了完整的技术参考。