# 生产级Vue.js组件开发指南与最佳实践

## 项目概述

本文档基于NCC Platform前端架构，提供Vue.js 2.x + Element UI的生产级组件开发规范、最佳实践和完整示例代码。

---

## 组件架构设计原则

### 1. 单一职责原则
每个组件只负责一个具体功能，保持组件的独立性和可复用性。

### 2. 组合优于继承  
通过组合多个小组件来构建复杂功能，而不是创建庞大的单体组件。

### 3. 响应式数据管理
合理使用Vuex进行状态管理，组件内部状态与全局状态分离。

### 4. 性能优化
合理使用computed、watch、v-memo等优化手段。

---

## 生产级组件示例

### 1. 项目积分管理组件

#### 1.1 项目列表组件 (ProjectScoreList.vue)

```vue
<template>
  <div class="project-score-container">
    <!-- 查询条件 -->
    <el-card class="box-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model="queryParams.projectName"
            placeholder="请输入项目名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="项目状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable style="width: 200px">
            <el-option
              v-for="dict in dict.type.project_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['web:project2score:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['web:project2score:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['web:project2score:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['web:project2score:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="projectList" 
        @selection-change="handleSelectionChange"
        :default-sort="{prop: 'createTime', order: 'descending'}"
        stripe
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目ID" align="center" prop="id" width="80" />
        <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" min-width="150">
          <template slot-scope="scope">
            <el-link class="link-type" @click="handleView(scope.row)">{{ scope.row.projectName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="项目类型" align="center" prop="projectType" width="100">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.project_type" :value="scope.row.projectType"/>
          </template>
        </el-table-column>
        <el-table-column label="是否团队" align="center" prop="isTeam" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isTeam === '1' ? 'success' : 'info'">
              {{ scope.row.isTeam === '1' ? '团队' : '个人' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="团队人数" align="center" prop="teamCount" width="80" />
        <el-table-column label="总积分" align="center" prop="totalScore" width="100">
          <template slot-scope="scope">
            <span class="score-highlight">{{ scope.row.totalScore }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <project-status-tag :status="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="createBy" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['web:project2score:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['web:project2score:edit']"
              v-if="scope.row.status === '0'"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleApprove(scope.row)"
              v-hasPermi="['web:project2score:approve']"
              v-if="scope.row.status === '1'"
            >审核</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['web:project2score:remove']"
              v-if="scope.row.status === '0'"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改项目积分对话框 -->
    <project-score-dialog
      :visible.sync="dialogVisible"
      :form-data="form"
      :is-edit="isEdit"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />

    <!-- 项目详情对话框 -->
    <project-detail-dialog
      :visible.sync="detailVisible"
      :project-id="selectedProjectId"
    />

    <!-- 审核对话框 -->
    <approval-dialog
      :visible.sync="approvalVisible"
      :project-data="selectedProject"
      @approve="handleApprovalSubmit"
    />
  </div>
</template>

<script>
import { 
  listProject2score, 
  getProject2score, 
  delProject2score, 
  addProject2score, 
  updateProject2score,
  approveProject2score
} from "@/api/web/project2score";
import ProjectScoreDialog from './components/ProjectScoreDialog.vue';
import ProjectDetailDialog from './components/ProjectDetailDialog.vue';
import ApprovalDialog from './components/ApprovalDialog.vue';
import ProjectStatusTag from './components/ProjectStatusTag.vue';

export default {
  name: "ProjectScoreList",
  dicts: ['project_status', 'project_type'],
  components: {
    ProjectScoreDialog,
    ProjectDetailDialog,
    ApprovalDialog,
    ProjectStatusTag
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目积分表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 是否显示详情弹出层
      detailVisible: false,
      // 是否显示审核弹出层
      approvalVisible: false,
      // 是否为编辑模式
      isEdit: false,
      // 选中的项目ID
      selectedProjectId: null,
      // 选中的项目数据
      selectedProject: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        status: null,
        createBy: null
      },
      // 表单数据
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        projectType: [
          { required: true, message: "项目类型不能为空", trigger: "change" }
        ],
        totalScore: [
          { required: true, message: "总积分不能为空", trigger: "blur" },
          { type: 'number', message: '总积分必须为数字值', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目积分列表 */
    async getList() {
      this.loading = true;
      try {
        const params = {
          ...this.queryParams,
          ...this.addDateRange(this.queryParams, this.dateRange)
        };
        const response = await listProject2score(params);
        this.projectList = response.rows;
        this.total = response.total;
      } catch (error) {
        this.$modal.msgError("查询失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    
    /** 取消按钮 */
    cancel() {
      this.dialogVisible = false;
      this.reset();
    },
    
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        projectName: null,
        projectType: null,
        description: null,
        isTeam: "0",
        teamCount: 1,
        totalScore: null,
        members: []
      };
      this.resetForm("form");
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.dialogVisible = true;
      this.isEdit = false;
      this.title = "添加项目积分";
    },
    
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      try {
        const response = await getProject2score(id);
        this.form = response.data;
        this.dialogVisible = true;
        this.isEdit = true;
        this.title = "修改项目积分";
      } catch (error) {
        this.$modal.msgError("获取数据失败：" + error.message);
      }
    },
    
    /** 查看详情 */
    handleView(row) {
      this.selectedProjectId = row.id;
      this.detailVisible = true;
    },
    
    /** 审核按钮操作 */
    handleApprove(row) {
      this.selectedProject = row;
      this.approvalVisible = true;
    },
    
    /** 提交表单 */
    async handleSubmit(formData) {
      try {
        if (this.isEdit) {
          await updateProject2score(formData);
          this.$modal.msgSuccess("修改成功");
        } else {
          await addProject2score(formData);
          this.$modal.msgSuccess("新增成功");
        }
        this.dialogVisible = false;
        this.getList();
      } catch (error) {
        this.$modal.msgError("操作失败：" + error.message);
      }
    },
    
    /** 审核提交 */
    async handleApprovalSubmit(approvalData) {
      try {
        await approveProject2score(approvalData);
        this.$modal.msgSuccess("审核完成");
        this.approvalVisible = false;
        this.getList();
      } catch (error) {
        this.$modal.msgError("审核失败：" + error.message);
      }
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const names = row.projectName || this.projectList.filter(item => this.ids.includes(item.id)).map(item => item.projectName).join('、');
      
      this.$modal.confirm('是否确认删除项目"' + names + '"？').then(async () => {
        try {
          await delProject2score(ids);
          this.getList();
          this.$modal.msgSuccess("删除成功");
        } catch (error) {
          this.$modal.msgError("删除失败：" + error.message);
        }
      }).catch(() => {});
    },
    
    /** 导出按钮操作 */
    handleExport() {
      const params = {
        ...this.queryParams,
        ...this.addDateRange(this.queryParams, this.dateRange)
      };
      this.$modal.confirm('是否确认导出所有项目积分数据项？').then(() => {
        this.download('web/project2score/export', params, `project2score_${new Date().getTime()}.xlsx`);
      });
    },
    
    /** 取消对话框 */
    handleCancel() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.project-score-container {
  padding: 20px;
  
  .box-card {
    margin-bottom: 20px;
  }
  
  .link-type {
    color: #409eff;
    cursor: pointer;
    
    &:hover {
      color: #66b1ff;
    }
  }
  
  .score-highlight {
    font-weight: bold;
    color: #e6a23c;
  }
  
  .el-table {
    .el-table__row:hover > td {
      background-color: #f5f7fa;
    }
  }
  
  .mb8 {
    margin-bottom: 8px;
  }
}
</style>
```

#### 1.2 项目积分表单组件 (ProjectScoreDialog.vue)

```vue
<template>
  <el-dialog 
    :title="isEdit ? '编辑项目积分' : '新增项目积分'" 
    :visible.sync="dialogVisible" 
    width="800px" 
    append-to-body
    :before-close="handleClose"
    custom-class="project-score-dialog"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <span class="card-title">基本信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input 
                v-model="form.projectName" 
                placeholder="请输入项目名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="projectType">
              <el-select v-model="form.projectType" placeholder="请选择项目类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.project_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否团队" prop="isTeam">
              <el-radio-group v-model="form.isTeam" @change="handleTeamChange">
                <el-radio label="0">个人项目</el-radio>
                <el-radio label="1">团队项目</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.isTeam === '1'">
            <el-form-item label="团队人数" prop="teamCount">
              <el-input-number 
                v-model="form.teamCount" 
                :min="2" 
                :max="10" 
                controls-position="right"
                style="width: 100%"
                @change="handleTeamCountChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总积分" prop="totalScore">
              <el-input-number 
                v-model="form.totalScore" 
                :precision="2" 
                :step="0.1" 
                :min="0" 
                :max="100"
                controls-position="right"
                style="width: 100%"
                @change="calculateMemberScores"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="项目描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入项目描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>

      <!-- 团队成员信息 -->
      <el-card class="box-card" shadow="never" v-if="form.isTeam === '1'">
        <div slot="header" class="clearfix">
          <span class="card-title">团队成员信息</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="addMember"
            :disabled="form.members.length >= form.teamCount"
          >
            <i class="el-icon-plus"></i> 添加成员
          </el-button>
        </div>
        
        <div v-if="form.members.length === 0" class="empty-members">
          <el-empty description="暂无团队成员，请添加成员信息" image-size="80"></el-empty>
        </div>
        
        <div v-for="(member, index) in form.members" :key="index" class="member-item">
          <el-card shadow="hover" class="member-card">
            <div slot="header" class="member-header">
              <span>成员 {{ index + 1 }}</span>
              <el-button 
                style="float: right; padding: 3px 0" 
                type="text" 
                @click="removeMember(index)"
                icon="el-icon-delete"
              >删除</el-button>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item 
                  :label="'成员姓名'" 
                  :prop="'members.' + index + '.memberName'"
                  :rules="memberRules.memberName"
                >
                  <el-input 
                    v-model="member.memberName" 
                    placeholder="请输入成员姓名"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item 
                  :label="'成员类型'" 
                  :prop="'members.' + index + '.memberType'"
                  :rules="memberRules.memberType"
                >
                  <el-select v-model="member.memberType" placeholder="请选择成员类型" style="width: 100%">
                    <el-option label="学生" value="学生" />
                    <el-option label="教师" value="教师" />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item 
                  :label="member.memberType === '学生' ? '学号' : '工号'" 
                  :prop="'members.' + index + '.memberId'"
                  :rules="memberRules.memberId"
                >
                  <el-input 
                    v-model="member.memberId" 
                    :placeholder="'请输入' + (member.memberType === '学生' ? '学号' : '工号')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item 
                  :label="'所属部门'" 
                  :prop="'members.' + index + '.department'"
                  :rules="memberRules.department"
                >
                  <el-input 
                    v-model="member.department" 
                    placeholder="请输入所属部门"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item 
                  :label="'积分占比'" 
                  :prop="'members.' + index + '.proportion'"
                  :rules="memberRules.proportion"
                >
                  <el-input-number 
                    v-model="member.proportion" 
                    :precision="3" 
                    :step="0.001" 
                    :min="0" 
                    :max="1"
                    controls-position="right"
                    style="width: 100%"
                    @change="calculateMemberScores"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item :label="'分配积分'">
                  <el-input 
                    :value="(member.proportion * form.totalScore).toFixed(2)" 
                    disabled
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </div>
        
        <!-- 积分分配汇总 -->
        <div v-if="form.members.length > 0" class="score-summary">
          <el-alert
            :title="`总占比: ${totalProportion.toFixed(3)} | 已分配积分: ${allocatedScore.toFixed(2)} | 剩余积分: ${remainingScore.toFixed(2)}`"
            :type="Math.abs(totalProportion - 1) < 0.001 ? 'success' : 'warning'"
            show-icon
            :closable="false"
          />
        </div>
      </el-card>

      <!-- 附件上传 -->
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <span class="card-title">相关附件</span>
        </div>
        
        <file-upload
          v-model="form.attachments"
          :limit="5"
          :file-size="10"
          :file-type="['doc', 'docx', 'pdf', 'jpg', 'png']"
          @change="handleFileChange"
        />
      </el-card>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "ProjectScoreDialog",
  dicts: ['project_type'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      form: {
        id: null,
        projectName: '',
        projectType: '',
        description: '',
        isTeam: '0',
        teamCount: 1,
        totalScore: 0,
        members: [],
        attachments: []
      },
      rules: {
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        projectType: [
          { required: true, message: '请选择项目类型', trigger: 'change' }
        ],
        totalScore: [
          { required: true, message: '总积分不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '积分范围为 0-100', trigger: 'blur' }
        ]
      },
      memberRules: {
        memberName: [
          { required: true, message: '成员姓名不能为空', trigger: 'blur' }
        ],
        memberType: [
          { required: true, message: '请选择成员类型', trigger: 'change' }
        ],
        memberId: [
          { required: true, message: '学号/工号不能为空', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '所属部门不能为空', trigger: 'blur' }
        ],
        proportion: [
          { required: true, message: '积分占比不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 1, message: '占比范围为 0-1', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    totalProportion() {
      return this.form.members.reduce((sum, member) => sum + (member.proportion || 0), 0);
    },
    allocatedScore() {
      return this.form.members.reduce((sum, member) => sum + ((member.proportion || 0) * this.form.totalScore), 0);
    },
    remainingScore() {
      return this.form.totalScore - this.allocatedScore;
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.form = { ...newVal };
          // 确保成员数组存在
          if (!this.form.members) {
            this.form.members = [];
          }
        }
      },
      immediate: true,
      deep: true
    },
    visible(newVal) {
      if (newVal && !this.isEdit) {
        this.resetForm();
      }
    }
  },
  methods: {
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        projectName: '',
        projectType: '',
        description: '',
        isTeam: '0',
        teamCount: 1,
        totalScore: 0,
        members: [],
        attachments: []
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    
    /** 团队类型变化处理 */
    handleTeamChange(value) {
      if (value === '0') {
        // 切换为个人项目，清空成员信息
        this.form.members = [];
        this.form.teamCount = 1;
      } else {
        // 切换为团队项目，初始化成员
        this.form.teamCount = 2;
        this.initializeMembers();
      }
    },
    
    /** 团队人数变化处理 */
    handleTeamCountChange(value) {
      const currentCount = this.form.members.length;
      if (value > currentCount) {
        // 增加成员
        const averageProportion = 1 / value;
        for (let i = currentCount; i < value; i++) {
          this.form.members.push({
            memberName: '',
            memberType: '学生',
            memberId: '',
            department: '',
            proportion: averageProportion
          });
        }
      } else if (value < currentCount) {
        // 减少成员
        this.form.members = this.form.members.slice(0, value);
      }
      this.redistributeProportions();
    },
    
    /** 初始化成员 */
    initializeMembers() {
      const memberCount = this.form.teamCount;
      const averageProportion = 1 / memberCount;
      
      this.form.members = Array.from({ length: memberCount }, () => ({
        memberName: '',
        memberType: '学生',
        memberId: '',
        department: '',
        proportion: averageProportion
      }));
    },
    
    /** 添加成员 */
    addMember() {
      if (this.form.members.length < this.form.teamCount) {
        const remainingProportion = 1 - this.totalProportion;
        this.form.members.push({
          memberName: '',
          memberType: '学生',
          memberId: '',
          department: '',
          proportion: Math.max(0, remainingProportion)
        });
      }
    },
    
    /** 删除成员 */
    removeMember(index) {
      this.form.members.splice(index, 1);
      this.form.teamCount = this.form.members.length;
      this.redistributeProportions();
    },
    
    /** 重新分配占比 */
    redistributeProportions() {
      if (this.form.members.length > 0) {
        const averageProportion = 1 / this.form.members.length;
        this.form.members.forEach(member => {
          member.proportion = averageProportion;
        });
      }
    },
    
    /** 计算成员积分 */
    calculateMemberScores() {
      // 在积分变化时实时计算每个成员的分配积分
      // 这个方法主要用于触发计算属性的重新计算
    },
    
    /** 文件上传变化处理 */
    handleFileChange(fileList) {
      this.form.attachments = fileList;
    },
    
    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;
        
        // 团队项目验证
        if (this.form.isTeam === '1') {
          if (this.form.members.length === 0) {
            this.$modal.msgError('团队项目必须添加成员信息');
            return;
          }
          
          // 验证积分占比总和
          if (Math.abs(this.totalProportion - 1) > 0.001) {
            this.$modal.msgError('团队成员积分占比总和必须等于1');
            return;
          }
          
          // 验证成员信息完整性
          for (let i = 0; i < this.form.members.length; i++) {
            const member = this.form.members[i];
            if (!member.memberName || !member.memberType || !member.memberId || !member.department) {
              this.$modal.msgError(`请完善第${i + 1}个成员的信息`);
              return;
            }
          }
        }
        
        this.submitLoading = true;
        try {
          const submitData = { ...this.form };
          // 计算每个成员的实际积分
          if (submitData.isTeam === '1') {
            submitData.members.forEach(member => {
              member.score = (member.proportion * submitData.totalScore).toFixed(2);
            });
          }
          
          this.$emit('submit', submitData);
        } catch (error) {
          this.$modal.msgError('提交失败：' + error.message);
        } finally {
          this.submitLoading = false;
        }
      });
    },
    
    /** 取消操作 */
    handleCancel() {
      this.$emit('cancel');
    },
    
    /** 关闭对话框前的处理 */
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.project-score-dialog {
  .box-card {
    margin-bottom: 20px;
    
    .card-title {
      font-weight: bold;
      color: #303133;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .member-item {
    margin-bottom: 15px;
    
    .member-card {
      border: 1px solid #e4e7ed;
      
      .member-header {
        font-weight: bold;
        color: #606266;
      }
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .empty-members {
    text-align: center;
    padding: 40px 0;
  }
  
  .score-summary {
    margin-top: 20px;
  }
  
  .dialog-footer {
    text-align: right;
  }
}

// 深度选择器修改Element UI样式
::v-deep {
  .el-card__header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }
  
  .el-input-number .el-input__inner {
    text-align: left;
  }
  
  .el-textarea__inner {
    resize: vertical;
  }
}
</style>
```

### 2. 通用组件示例

#### 2.1 状态标签组件 (ProjectStatusTag.vue)

```vue
<template>
  <el-tag :type="tagType" :effect="effect" size="small">
    {{ statusText }}
  </el-tag>
</template>

<script>
export default {
  name: 'ProjectStatusTag',
  props: {
    status: {
      type: [String, Number],
      required: true
    },
    effect: {
      type: String,
      default: 'light'
    }
  },
  computed: {
    statusConfig() {
      const configs = {
        '0': { text: '草稿', type: 'info' },
        '1': { text: '待审核', type: 'warning' },
        '2': { text: '已通过', type: 'success' },
        '3': { text: '已驳回', type: 'danger' }
      };
      return configs[String(this.status)] || { text: '未知', type: 'info' };
    },
    statusText() {
      return this.statusConfig.text;
    },
    tagType() {
      return this.statusConfig.type;
    }
  }
};
</script>
```

#### 2.2 文件上传组件 (FileUpload.vue)

```vue
<template>
  <div class="upload-file">
    <el-upload
      ref="upload"
      :action="uploadFileUrl"
      :headers="headers"
      :file-list="fileList"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :on-remove="handleDelete"
      :on-exceed="handleExceed"
      :limit="limit"
      :accept="accept"
      drag
      multiple
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <div class="el-upload__tip" slot="tip">
        <template v-if="showTip">
          请上传
          <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
          <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
          的文件，最多上传 <b style="color: #f56c6c">{{ limit }}</b> 个文件
        </template>
      </div>
    </el-upload>
    
    <!-- 文件列表展示 -->
    <transition-group v-if="showFileList" name="list" tag="ul" class="el-upload-list">
      <li
        v-for="(file, index) in fileList"
        :key="file.uid || index"
        class="el-upload-list__item is-success"
      >
        <a class="el-upload-list__item-name" @click="handleDownload(file)">
          <i class="el-icon-document"></i>{{ file.name }}
        </a>
        <label class="el-upload-list__item-status-label">
          <i class="el-icon-upload-success el-icon-circle-check"></i>
        </label>
        <i class="el-icon-close" @click="handleDelete(file, index)"></i>
        <i class="el-icon-close-tip">按 delete 键可删除</i>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "FileUpload",
  props: {
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: []
    };
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
    // 接受上传的文件类型
    accept() {
      return this.fileType ? this.fileType.map(type => `.${type}`).join(',') : '';
    },
    // 是否显示文件列表
    showFileList() {
      return this.fileList && this.fileList.length > 0;
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',');
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType && this.fileType.length > 0) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExt && fileExt.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.fileName, url: res.fileName });
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.upload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 删除文件
    handleDelete(file, index) {
      if (index != undefined) {
        this.fileList.splice(index, 1);
      } else {
        const findex = this.fileList.map(f => f.uid).indexOf(file.uid);
        if (findex > -1) {
          this.fileList.splice(findex, 1);
        }
      }
      this.$emit("input", this.listToString(this.fileList));
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 预览
    handleDownload(file) {
      const name = file.name;
      if (name) {
        window.open(this.baseUrl + "/common/download/resource?resource=" + encodeURI(name), "_blank");
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        if (list[i].url) {
          strs += list[i].url + separator;
        }
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    }
  }
};
</script>

<style scoped lang="scss">
.upload-file {
  .el-upload {
    .el-upload-dragger {
      width: 100%;
    }
  }
  
  .el-upload-list {
    max-height: 200px;
    overflow-y: auto;
    
    .el-upload-list__item {
      position: relative;
      margin-bottom: 10px;
      padding: 8px 10px;
      border: 1px solid #c0ccda;
      border-radius: 5px;
      line-height: 2;
      background: #fff;
      
      .el-upload-list__item-name {
        color: #606266;
        font-size: 14px;
        cursor: pointer;
        
        &:hover {
          color: #409eff;
        }
        
        .el-icon-document {
          margin-right: 6px;
          color: #909399;
        }
      }
      
      .el-icon-close {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
        opacity: 0.75;
        color: #606266;
        
        &:hover {
          opacity: 1;
          color: #f56c6c;
        }
      }
    }
  }
}

.list-enter-active, .list-leave-active {
  transition: all 0.5s;
}

.list-enter, .list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
```

### 3. 数据可视化组件

#### 3.1 图表组件 (ScoreChart.vue)

```vue
<template>
  <div class="score-chart">
    <el-card shadow="hover">
      <div slot="header" class="chart-header">
        <span class="chart-title">{{ title }}</span>
        <div class="chart-controls">
          <el-select v-model="chartType" size="small" @change="handleChartTypeChange">
            <el-option label="柱状图" value="bar"></el-option>
            <el-option label="折线图" value="line"></el-option>
            <el-option label="饼图" value="pie"></el-option>
          </el-select>
          <el-button size="small" @click="refreshChart" icon="el-icon-refresh">刷新</el-button>
        </div>
      </div>
      
      <div ref="chart" class="chart-container" v-loading="loading"></div>
      
      <div class="chart-legend" v-if="showLegend">
        <div class="legend-item" v-for="item in legendData" :key="item.name">
          <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
          <span class="legend-text">{{ item.name }}: {{ item.value }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from 'lodash-es';

export default {
  name: 'ScoreChart',
  props: {
    title: {
      type: String,
      default: '数据统计'
    },
    data: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'bar'
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null,
      chartType: this.type,
      legendData: []
    };
  },
  computed: {
    chartOptions() {
      const options = {
        tooltip: {
          trigger: this.chartType === 'pie' ? 'item' : 'axis',
          formatter: this.tooltipFormatter
        },
        legend: {
          data: this.data.map(item => item.name),
          orient: 'horizontal',
          left: 'center',
          top: 20
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      };

      if (this.chartType === 'pie') {
        options.series = [
          {
            name: this.title,
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.data.map(item => ({
              value: item.value,
              name: item.name
            }))
          }
        ];
      } else {
        options.xAxis = {
          type: 'category',
          data: this.data.map(item => item.name),
          axisTick: {
            alignWithLabel: true
          }
        };
        options.yAxis = {
          type: 'value'
        };
        options.series = [
          {
            name: this.title,
            type: this.chartType,
            data: this.data.map(item => item.value),
            smooth: this.chartType === 'line',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            }
          }
        ];
      }

      return options;
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    type(newVal) {
      this.chartType = newVal;
      this.updateChart();
    }
  },
  mounted() {
    this.initChart();
    this.addResizeListener();
  },
  beforeDestroy() {
    this.removeResizeListener();
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart);
      this.updateChart();
    },
    
    updateChart() {
      if (this.chart && this.data.length > 0) {
        this.chart.setOption(this.chartOptions, true);
        this.updateLegendData();
      }
    },
    
    updateLegendData() {
      if (this.showLegend) {
        this.legendData = this.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          color: this.getColor(index)
        }));
      }
    },
    
    getColor(index) {
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452'];
      return colors[index % colors.length];
    },
    
    tooltipFormatter(params) {
      if (this.chartType === 'pie') {
        return `${params.name}: ${params.value} (${params.percent}%)`;
      } else {
        return `${params.name}: ${params.value}`;
      }
    },
    
    handleChartTypeChange(type) {
      this.chartType = type;
      this.updateChart();
      this.$emit('chart-type-change', type);
    },
    
    refreshChart() {
      this.$emit('refresh');
    },
    
    addResizeListener() {
      this.resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 300);
      window.addEventListener('resize', this.resizeHandler);
    },
    
    removeResizeListener() {
      if (this.resizeHandler) {
        window.removeEventListener('resize', this.resizeHandler);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.score-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
    
    .chart-controls {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .chart-container {
    height: v-bind(height);
    min-height: 300px;
  }
  
  .chart-legend {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    
    .legend-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
      }
      
      .legend-text {
        color: #606266;
      }
    }
  }
}
</style>
```

---

## Vuex状态管理最佳实践

### 1. 模块化状态管理

```javascript
// store/modules/project.js
const state = {
  projectList: [],
  currentProject: null,
  loading: false,
  searchParams: {
    projectName: '',
    status: '',
    pageNum: 1,
    pageSize: 10
  }
};

const mutations = {
  SET_PROJECT_LIST(state, list) {
    state.projectList = list;
  },
  SET_CURRENT_PROJECT(state, project) {
    state.currentProject = project;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_SEARCH_PARAMS(state, params) {
    state.searchParams = { ...state.searchParams, ...params };
  },
  RESET_SEARCH_PARAMS(state) {
    state.searchParams = {
      projectName: '',
      status: '',
      pageNum: 1,
      pageSize: 10
    };
  }
};

const actions = {
  // 获取项目列表
  async fetchProjectList({ commit, state }, params = {}) {
    commit('SET_LOADING', true);
    try {
      const searchParams = { ...state.searchParams, ...params };
      const response = await listProject2score(searchParams);
      commit('SET_PROJECT_LIST', response.rows || []);
      return response;
    } catch (error) {
      throw error;
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // 获取项目详情
  async fetchProjectDetail({ commit }, id) {
    try {
      const response = await getProject2score(id);
      commit('SET_CURRENT_PROJECT', response.data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  
  // 更新搜索参数
  updateSearchParams({ commit }, params) {
    commit('SET_SEARCH_PARAMS', params);
  },
  
  // 重置搜索参数
  resetSearchParams({ commit }) {
    commit('RESET_SEARCH_PARAMS');
  }
};

const getters = {
  // 过滤后的项目列表
  filteredProjectList: (state) => (filter) => {
    if (!filter) return state.projectList;
    return state.projectList.filter(project => {
      return Object.keys(filter).every(key => {
        if (filter[key] === null || filter[key] === '') return true;
        return project[key] === filter[key];
      });
    });
  },
  
  // 项目统计信息
  projectStatistics: (state) => {
    const list = state.projectList;
    return {
      total: list.length,
      completed: list.filter(p => p.status === '2').length,
      pending: list.filter(p => p.status === '1').length,
      draft: list.filter(p => p.status === '0').length,
      totalScore: list.reduce((sum, p) => sum + (p.totalScore || 0), 0)
    };
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
```

### 2. 组合式API使用示例

```javascript
// composables/useProject.js
import { computed, ref, onMounted } from '@vue/composition-api';
import { useStore } from 'vuex';

export function useProject() {
  const store = useStore();
  const loading = ref(false);
  
  // 状态
  const projectList = computed(() => store.state.project.projectList);
  const currentProject = computed(() => store.state.project.currentProject);
  const searchParams = computed(() => store.state.project.searchParams);
  const statistics = computed(() => store.getters['project/projectStatistics']);
  
  // 方法
  const fetchProjects = async (params = {}) => {
    loading.value = true;
    try {
      await store.dispatch('project/fetchProjectList', params);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };
  
  const fetchProjectDetail = async (id) => {
    try {
      return await store.dispatch('project/fetchProjectDetail', id);
    } catch (error) {
      console.error('获取项目详情失败:', error);
      throw error;
    }
  };
  
  const updateSearchParams = (params) => {
    store.dispatch('project/updateSearchParams', params);
  };
  
  const resetSearch = () => {
    store.dispatch('project/resetSearchParams');
  };
  
  return {
    // 状态
    projectList,
    currentProject,
    searchParams,
    statistics,
    loading,
    
    // 方法
    fetchProjects,
    fetchProjectDetail,
    updateSearchParams,
    resetSearch
  };
}
```

---

## 性能优化最佳实践

### 1. 组件懒加载

```javascript
// router/index.js
const routes = [
  {
    path: '/project',
    component: () => import('@/layout/index'),
    children: [
      {
        path: 'score',
        name: 'ProjectScore',
        component: () => import('@/views/web/project2score/index'),
        meta: { title: '项目积分', keepAlive: true }
      }
    ]
  }
];
```

### 2. 虚拟滚动列表

```vue
<template>
  <div class="virtual-list-container">
    <virtual-list
      :size="itemSize"
      :remain="remain"
      :items="items"
      :item="itemComponent"
      :scroll="scrollOptions"
    />
  </div>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list';
import ProjectItem from './ProjectItem.vue';

export default {
  name: 'ProjectVirtualList',
  components: {
    VirtualList
  },
  props: {
    items: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      itemSize: 80,
      remain: 10,
      itemComponent: ProjectItem,
      scrollOptions: {
        throttle: 16
      }
    };
  }
};
</script>
```

### 3. 防抖和节流

```javascript
// utils/performance.js
import { debounce, throttle } from 'lodash-es';

export const searchDebounce = debounce((callback, params) => {
  callback(params);
}, 300);

export const scrollThrottle = throttle((callback) => {
  callback();
}, 100);

// 在组件中使用
export default {
  methods: {
    handleSearch() {
      searchDebounce(this.doSearch, this.searchParams);
    },
    
    handleScroll() {
      scrollThrottle(this.updateScrollPosition);
    }
  }
};
```

---

## 错误处理与日志记录

### 1. 全局错误处理

```javascript
// plugins/errorHandler.js
import Vue from 'vue';

Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err);
  console.error('Component:', vm);
  console.error('Error Info:', info);
  
  // 发送错误信息到日志系统
  if (process.env.NODE_ENV === 'production') {
    // sendErrorLog({
    //   error: err.message,
    //   stack: err.stack,
    //   component: vm.$options.name,
    //   info: info
    // });
  }
};

// 全局异常处理
window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled Promise Rejection:', event.reason);
  if (process.env.NODE_ENV === 'production') {
    // sendErrorLog({
    //   error: 'Unhandled Promise Rejection',
    //   reason: event.reason
    // });
  }
});
```

### 2. 组件级错误边界

```vue
<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-content">
      <el-alert
        title="组件加载失败"
        type="error"
        :description="errorMessage"
        show-icon
        :closable="false"
      />
      <el-button @click="retry" type="primary" size="small" style="margin-top: 10px">
        重试
      </el-button>
    </div>
    <slot v-else />
  </div>
</template>

<script>
export default {
  name: 'ErrorBoundary',
  data() {
    return {
      hasError: false,
      errorMessage: ''
    };
  },
  errorCaptured(err, vm, info) {
    this.hasError = true;
    this.errorMessage = err.message || '未知错误';
    console.error('Component Error Captured:', err, vm, info);
    return false;
  },
  methods: {
    retry() {
      this.hasError = false;
      this.errorMessage = '';
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    }
  }
};
</script>
```

---

本文档提供了NCC Platform前端开发的完整指南，包含了生产级组件示例、状态管理、性能优化和错误处理等最佳实践，为团队开发提供了标准化的技术规范。